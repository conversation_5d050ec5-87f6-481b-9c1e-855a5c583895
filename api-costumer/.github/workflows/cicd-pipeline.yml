name: Deploy Api Costumer

on:
  push:
    branches:
      - main
      
jobs:
  CI-CD:
    uses: vitola-lanches/actions/.github/workflows/deploy-java.yml@main
    with:
      service_name: api-costumer
      dockerfile_path: dockerfiles/Dockerfile-java
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      CLUSTER_NAME: ${{ secrets.CLUSTER_NAME }}
      DB_USERNAME: ${{ secrets.DB_USERNAME }}
      DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}
      SPRING_DATASOURCE_URL: ${{ secrets.SPRING_DATASOURCE_URL }}
      SPRING_REDIS_HOST: ${{ secrets.SPRING_REDIS_HOST }}
      SPRING_REDIS_PORT: ${{ secrets.SPRING_REDIS_PORT }}