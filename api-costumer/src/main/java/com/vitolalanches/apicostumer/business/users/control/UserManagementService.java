package com.vitolalanches.apicostumer.business.users.control;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.vitolalanches.apicostumer.business.users.dto.UserDto;
import com.vitolalanches.apicostumer.business.users.dto.UserInfoDto;
import com.vitolalanches.apicostumer.business.users.entities.User;
import com.vitolalanches.apicostumer.shared.CacheService;
import com.vitolalanches.apicostumer.shared.VitolaException;

import jakarta.transaction.Transactional;

@Service
@Transactional
public class UserManagementService {
    private static final Logger LOG = LoggerFactory.getLogger(UserManagementService.class);

    private final UserRepository userRepository;
    private final CacheService cacheService;
    
    public UserManagementService(UserRepository userRepository, CacheService cacheService) {
        this.userRepository = userRepository;
        this.cacheService = cacheService;
    }
    
    public Boolean save(UserDto userDto) {
        if (userDto == null) {
            throw new VitolaException("USER_IS_NULL", 
                "Dados incorretos, por favor tente novamente");
        }
        
        UserDto.validate(userDto);
        
        if (userRepository.existsByDocument(userDto.document())) {
            throw new VitolaException("USER_ALREADY_EXISTS", 
                "Já existe um usuário cadastrado com este documento");
        }
        
        try {
            User user = new User();
            user.setDocument(userDto.document());
            user.setEmail(userDto.email());
            user.setFullName(userDto.fullName());
            
            User savedUser = userRepository.save(user);
            
            cacheService.getOrCreate("users", String.format("USER_%s_INFO", userDto.document()), 
                () -> savedUser);
            LOG.info("Usuário criado com sucesso");
            return true;
        } catch (Exception e) {
            LOG.error("Erro ao criar usuário: {}", e.getMessage());
            throw e;
        }
    }
    
    public UserInfoDto findByDocument(String document) {
        LOG.info("Buscando usuário por documento: {}", document);
        
        if (document == null || document.trim().isEmpty()) {
            throw new VitolaException("USER_DOCUMENT_IS_NULL", "Documento não preenchido para pesquisar");
        }
        
        User cachedUser = cacheService.getOrCreate("users", String.format("USER_%s_INFO", document), 
            () -> {
                Optional<User> userOpt = userRepository.findByDocument(document);
                if (userOpt.isEmpty()) {
                    throw new VitolaException("USER_NOT_FOUND", "Usuário não encontrado pelo documento informado");
                }
                return userOpt.get();
            });
        
        return new UserInfoDto(cachedUser.getId(), cachedUser.getDocument(), 
                              cachedUser.getEmail(), cachedUser.getFullName());
    }
}
