package com.vitolalanches.apicostumer.business.users.dto;

import com.vitolalanches.apicostumer.shared.VitolaException;

public record UserDto(String document, String email, String fullName) {
    
    public static void validate(UserDto userDto) {
        if (userDto.document() == null || userDto.document().trim().isEmpty()) {
            throw new VitolaException("USER_DOCUMENT_IS_NULL", "Documento é obrigatório");
        }
        if (userDto.email() == null || userDto.email().trim().isEmpty()) {
            throw new VitolaException("USER_EMAIL_IS_NULL", "Email é obrigatório");
        }
        if (userDto.fullName() == null || userDto.fullName().trim().isEmpty()) {
            throw new VitolaException("USER_NAME_IS_NULL", "Nome é obrigatório");
        }
    }
}
