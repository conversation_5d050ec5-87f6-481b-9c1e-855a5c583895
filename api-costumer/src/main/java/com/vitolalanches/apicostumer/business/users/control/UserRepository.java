package com.vitolalanches.apicostumer.business.users.control;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.vitolalanches.apicostumer.business.users.entities.User;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    boolean existsByDocument(String document);
    Optional<User> findByDocument(String document);
}
