package com.vitolalanches.apicostumer.business.users.controllers;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitolalanches.apicostumer.business.users.control.UserManagementService;
import com.vitolalanches.apicostumer.business.users.dto.UserDto;
import com.vitolalanches.apicostumer.business.users.dto.UserInfoDto;
import com.vitolalanches.apicostumer.shared.ApiReturn;
import com.vitolalanches.apicostumer.shared.BaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/users")
@Tag(name = "Usuários")
@Validated
public class UserController extends BaseController {
    
    private final UserManagementService userService;
    
    public UserController(Executor executor, UserManagementService userService) {
        super(executor);
        this.userService = userService;
    }
    
    @PostMapping
    @Operation(summary = "Criar usuário")
    @ApiResponse(responseCode = "201", description = "Usuário criado com sucesso")
    public ApiReturn<Boolean> create(@Valid @RequestBody UserDto user) {
        
        return ApiReturn.of(this.userService.save(user));
    }
    
    @GetMapping("/{document}/info")
    @Operation(summary = "Buscar usuário por documento")
    @Parameter(name = "document", description = "Documento do usuario", example = "12345678900")
    @ApiResponse(responseCode = "200", description = "Usuário encontrado")
    @ApiResponse(responseCode = "404", description = "Usuário não encontrado")
    public CompletableFuture<ApiReturn<UserInfoDto>> findByDocument(@PathVariable String document) {
        return asyncResultOf(() -> this.userService.findByDocument(document));
    }
}
