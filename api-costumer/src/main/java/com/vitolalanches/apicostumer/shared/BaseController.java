package com.vitolalanches.apicostumer.shared;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public abstract class BaseController {
    
    private static final Logger LOG = LoggerFactory.getLogger(BaseController.class);
    
    private final Executor executor;
    
    protected BaseController(Executor executor) {
        this.executor = executor;
    }
    
    protected <T> CompletableFuture<ApiReturn<T>> asyncResultOf(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                T result = supplier.get();
                return ApiReturn.of(result);
            } catch (Exception e) {
                LOG.error("Error in async operation: {}", e.getMessage(), e);
                throw e;
            }
        }, executor);
    }
    
    protected <T> CompletableFuture<ResponseEntity<ApiReturn<T>>> asyncResponseOf(Supplier<T> supplier) {
        return asyncResultOf(supplier)
            .thenApply(result -> ResponseEntity.ok(result))
            .exceptionally(throwable -> {
                LOG.error("Error in async response: {}", throwable.getMessage(), throwable);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiReturn.error("INTERNAL_ERROR"));
            });
    }
}
