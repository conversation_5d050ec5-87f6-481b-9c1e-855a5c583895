package com.vitolalanches.apicostumer.shared;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(VitolaException.class)
    public ResponseEntity<Map<String, Object>> handleVitolaException(VitolaException ex) {
        LOG.error("VitolaException: {} - {}", ex.getErrorCode(), ex.getMessage());
        
        HttpStatus status = mapErrorCodeToHttpStatus(ex.getErrorCode());
        
        Map<String, Object> response = Map.of(
            "error", ex.getErrorCode(),
            "message", ex.getMessage(),
            "traceId", ex.getTraceId()
        );
        
        return ResponseEntity.status(status).body(response);
    }
    
    private HttpStatus mapErrorCodeToHttpStatus(String errorCode) {
        return switch (errorCode) {
            case "USER_NOT_FOUND" -> HttpStatus.NOT_FOUND;
            case "USER_DOCUMENT_IS_NULL", "USER_IS_NULL", "USER_ALREADY_EXISTS" -> HttpStatus.BAD_REQUEST;
            default -> HttpStatus.INTERNAL_SERVER_ERROR;
        };
    }
}
