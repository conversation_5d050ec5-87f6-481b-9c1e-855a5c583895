package com.vitolalanches.apicostumer.shared;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ApiReturn<T> {
    private Boolean success;
    @JsonProperty("return")
    private T result;
    private String error;

    public ApiReturn() { }

    public ApiReturn(T result) {
        this.success = true;
        this.result = result;
        this.error = null;
    }

    private ApiReturn(String error) {
        this.success = false;
        this.result = null;
        this.error = error;
    }

    public static <T> ApiReturn<T> of(T t) {
        return new ApiReturn<>(t);
    }
    
    public static <T> ApiReturn<T> error(String error) {
        return new ApiReturn<>(error);
    }
    
    // Getters
    public Boolean getSuccess() {
        return success;
    }
    
    public T getResult() {
        return result;
    }
    
    public String getError() {
        return error;
    }
    
    // Setters
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public void setResult(T result) {
        this.result = result;
    }
    
    public void setError(String error) {
        this.error = error;
    }
}
