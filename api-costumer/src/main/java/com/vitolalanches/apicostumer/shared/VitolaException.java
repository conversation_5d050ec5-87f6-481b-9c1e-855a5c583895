package com.vitolalanches.apicostumer.shared;

import java.util.UUID;

public class VitolaException extends RuntimeException {
    private final String errorCode;
    private final String traceId;
    private final String contextName;
    private Object data;
    
    public VitolaException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.traceId = UUID.randomUUID().toString();
        this.contextName = "";
    }
    
    public VitolaException(String errorCode, String message, String contextName, String traceId) {
        super(message);
        this.errorCode = errorCode;
        this.contextName = contextName;
        this.traceId = traceId;
    }
    
    // getters
    public String getErrorCode() { return errorCode; }
    public String getTraceId() { return traceId; }
    public String getContextName() { return contextName; }
    public Object getData() { return data; }
    
    public VitolaException withData(Object data) {
        this.data = data;
        return this;
    }
}