package com.vitolalanches.apicostumer.shared;

import java.util.function.Supplier;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

@Service
public class CacheService {
    
    private final CacheManager cacheManager;

    public CacheService(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    public <T> T getOrCreate(String cacheName, String key, Supplier<T> supplier) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            throw new RuntimeException("Cache " + cacheName + " not found");
        }

        T value = cache.get(key, (Class<T>) Object.class);
        if (value != null) {
            return value;
        }

        value = supplier.get();
        if (value != null) {
            cache.put(key, value);
        }

        return value;
    }
}
