spring:
  datasource:
    url: ******************************************
    username: ${DB_USERNAME:admin}
    password: ${DB_PASSWORD:admin-pwd}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: create
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:} # deixe vazio se não tiver senha
