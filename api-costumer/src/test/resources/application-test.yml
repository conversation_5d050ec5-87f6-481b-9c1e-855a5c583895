spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: false
    defer-datasource-initialization: true
    properties:
      hibernate:
        hbm2ddl:
          auto: create-drop
        format_sql: true
  
  cache:
    type: simple

logging:
  level:
    com.vitolalanches: DEBUG
    org.springframework.cache: DEBUG
    org.hibernate.SQL: DEBUG
