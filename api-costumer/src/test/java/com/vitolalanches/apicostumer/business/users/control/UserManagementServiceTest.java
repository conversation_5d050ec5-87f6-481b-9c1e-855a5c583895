package com.vitolalanches.apicostumer.business.users.control;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.vitolalanches.apicostumer.business.users.dto.UserDto;
import com.vitolalanches.apicostumer.business.users.dto.UserInfoDto;
import com.vitolalanches.apicostumer.business.users.entities.User;
import com.vitolalanches.apicostumer.shared.CacheService;
import com.vitolalanches.apicostumer.shared.VitolaException;

@ExtendWith(MockitoExtension.class)
@DisplayName("UserManagementService")
class UserManagementServiceTest {

    @Mock
    private UserRepository userRepository;
    
    @Mock
    private CacheService cacheService;
    
    @InjectMocks
    private UserManagementService userService;
    
    private UserDto validUserDto;
    private User validUser;
    
    @BeforeEach
    void setUp() {
        validUserDto = new UserDto("***********", "<EMAIL>", "Test User");
        validUser = new User();
        validUser.setId(1L);
        validUser.setDocument("***********");
        validUser.setEmail("<EMAIL>");
        validUser.setFullName("Test User");
    }
    
    @Nested
    @DisplayName("Given I want to save a user")
    class SaveUserTests {
        
        @Test
        @DisplayName("When user data is valid and document doesn't exist, Then should save successfully")
        void shouldSaveUserSuccessfully() {
            // Given
            when(userRepository.existsByDocument(validUserDto.document())).thenReturn(false);
            when(userRepository.save(any(User.class))).thenReturn(validUser);
            when(cacheService.getOrCreate(anyString(), anyString(), any())).thenReturn(validUser);
            
            // When & Then
            assertDoesNotThrow(() -> userService.save(validUserDto));
            
            verify(userRepository).existsByDocument(validUserDto.document());
            verify(userRepository).save(any(User.class));
            verify(cacheService).getOrCreate(eq("users"), eq("USER_***********_INFO"), any());
        }
        
        @Test
        @DisplayName("When user data is null, Then should throw VitolaException")
        void shouldThrowExceptionWhenUserIsNull() {
            // When & Then
            VitolaException exception = assertThrows(VitolaException.class, 
                () -> userService.save(null));
            
            assertEquals("USER_IS_NULL", exception.getErrorCode());
            assertEquals("Dados incorretos, por favor tente novamente", exception.getMessage());
        }
        
        @Test
        @DisplayName("When document already exists, Then should throw VitolaException")
        void shouldThrowExceptionWhenDocumentExists() {
            // Given
            when(userRepository.existsByDocument(validUserDto.document())).thenReturn(true);
            
            // When & Then
            VitolaException exception = assertThrows(VitolaException.class, 
                () -> userService.save(validUserDto));
            
            assertEquals("USER_ALREADY_EXISTS", exception.getErrorCode());
            assertEquals("Já existe um usuário cadastrado com este documento", exception.getMessage());
        }
    }
    
    @Nested
    @DisplayName("Given I want to find a user by document")
    class FindUserTests {
        
        @Test
        @DisplayName("When document exists, Then should return user info")
        void shouldReturnUserInfoWhenDocumentExists() {
            // Given
            when(cacheService.getOrCreate(anyString(), anyString(), any())).thenReturn(validUser);
            
            // When
            UserInfoDto result = userService.findByDocument("***********");
            
            // Then
            assertNotNull(result);
            assertEquals(validUser.getId(), result.id());
            assertEquals(validUser.getDocument(), result.document());
            assertEquals(validUser.getEmail(), result.email());
            assertEquals(validUser.getFullName(), result.fullName());
        }
        
        @Test
        @DisplayName("When document is null, Then should throw VitolaException")
        void shouldThrowExceptionWhenDocumentIsNull() {
            // When & Then
            VitolaException exception = assertThrows(VitolaException.class, 
                () -> userService.findByDocument(null));
            
            assertEquals("USER_DOCUMENT_IS_NULL", exception.getErrorCode());
            assertEquals("Documento não preenchido para pesquisar", exception.getMessage());
        }
        
        @Test
        @DisplayName("When document is blank, Then should throw VitolaException")
        void shouldThrowExceptionWhenDocumentIsBlank() {
            // When & Then
            VitolaException exception = assertThrows(VitolaException.class, 
                () -> userService.findByDocument(""));
            
            assertEquals("USER_DOCUMENT_IS_NULL", exception.getErrorCode());
        }
    }
}
