package com.vitolalanches.apicostumer.business.users.controllers;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitolalanches.apicostumer.business.users.control.UserManagementService;
import com.vitolalanches.apicostumer.business.users.dto.UserDto;
import com.vitolalanches.apicostumer.business.users.dto.UserInfoDto;
import com.vitolalanches.apicostumer.config.TestConfig;
import com.vitolalanches.apicostumer.shared.GlobalExceptionHandler;
import com.vitolalanches.apicostumer.shared.VitolaException;

import java.util.concurrent.Executor;

@WebMvcTest(UserController.class)
@Import({TestConfig.class, GlobalExceptionHandler.class})
@ActiveProfiles("test")
@DisplayName("UserController")
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserManagementService userService;
    
    @MockBean
    private Executor executor;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Nested
    @DisplayName("Given I want to test basic controller functionality")
    class BasicTests {
        
        // @Test
        // @DisplayName("When controller is loaded, Then should not be null")
        // void shouldLoadController() {
        //     // Then
        //     assertNotNull(mockMvc);
        //     assertNotNull(userService);
        // }
    }
}
