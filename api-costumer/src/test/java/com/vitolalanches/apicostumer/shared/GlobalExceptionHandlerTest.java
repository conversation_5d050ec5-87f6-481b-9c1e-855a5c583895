package com.vitolalanches.apicostumer.shared;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@DisplayName("GlobalExceptionHandler")
class GlobalExceptionHandlerTest {

    private GlobalExceptionHandler exceptionHandler;
    
    @BeforeEach
    void setUp() {
        exceptionHandler = new GlobalExceptionHandler();
    }
    
    @Nested
    @DisplayName("Given I want to handle VitolaException")
    class HandleVitolaExceptionTests {
        
        @Test
        @DisplayName("When handling USER_NOT_FOUND error, Then should return 404 status")
        void shouldReturn404ForUserNotFound() {
            // Given
            VitolaException exception = new VitolaException("USER_NOT_FOUND", 
                "Usuário não encontrado pelo documento informado");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("USER_NOT_FOUND", body.get("error"));
            assertEquals("Usuário não encontrado pelo documento informado", body.get("message"));
            assertEquals(exception.getTraceId(), body.get("traceId"));
        }
        
        @Test
        @DisplayName("When handling USER_DOCUMENT_IS_NULL error, Then should return 400 status")
        void shouldReturn400ForUserDocumentIsNull() {
            // Given
            VitolaException exception = new VitolaException("USER_DOCUMENT_IS_NULL", 
                "Documento não preenchido para pesquisar");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("USER_DOCUMENT_IS_NULL", body.get("error"));
            assertEquals("Documento não preenchido para pesquisar", body.get("message"));
            assertEquals(exception.getTraceId(), body.get("traceId"));
        }
        
        @Test
        @DisplayName("When handling USER_IS_NULL error, Then should return 400 status")
        void shouldReturn400ForUserIsNull() {
            // Given
            VitolaException exception = new VitolaException("USER_IS_NULL", 
                "Dados incorretos, por favor tente novamente");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("USER_IS_NULL", body.get("error"));
            assertEquals("Dados incorretos, por favor tente novamente", body.get("message"));
        }
        
        @Test
        @DisplayName("When handling USER_ALREADY_EXISTS error, Then should return 400 status")
        void shouldReturn400ForUserAlreadyExists() {
            // Given
            VitolaException exception = new VitolaException("USER_ALREADY_EXISTS", 
                "Já existe um usuário cadastrado com este documento");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("USER_ALREADY_EXISTS", body.get("error"));
            assertEquals("Já existe um usuário cadastrado com este documento", body.get("message"));
        }
        
        @Test
        @DisplayName("When handling unknown error code, Then should return 500 status")
        void shouldReturn500ForUnknownError() {
            // Given
            VitolaException exception = new VitolaException("UNKNOWN_ERROR", 
                "Something went wrong");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("UNKNOWN_ERROR", body.get("error"));
            assertEquals("Something went wrong", body.get("message"));
            assertEquals(exception.getTraceId(), body.get("traceId"));
        }
        
        @Test
        @DisplayName("When handling exception with context and traceId, Then should preserve all info")
        void shouldPreserveAllExceptionInfo() {
            // Given
            String traceId = "custom-trace-123";
            String contextName = "UserService";
            VitolaException exception = new VitolaException("USER_NOT_FOUND", 
                "User not found", contextName, traceId);
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("USER_NOT_FOUND", body.get("error"));
            assertEquals("User not found", body.get("message"));
            assertEquals(traceId, body.get("traceId"));
        }
        
        @Test
        @DisplayName("When response body is created, Then should contain exactly 3 fields")
        void shouldContainExactlyThreeFields() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            
            // When
            ResponseEntity<Map<String, Object>> response = exceptionHandler.handleVitolaException(exception);
            
            // Then
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals(3, body.size());
            assertTrue(body.containsKey("error"));
            assertTrue(body.containsKey("message"));
            assertTrue(body.containsKey("traceId"));
        }
    }
    
    @Nested
    @DisplayName("Given I want to test error code mapping")
    class ErrorCodeMappingTests {
        
        @Test
        @DisplayName("When testing all known error codes, Then should map to correct HTTP status")
        void shouldMapAllKnownErrorCodes() {
            // Test NOT_FOUND mapping
            VitolaException notFoundEx = new VitolaException("USER_NOT_FOUND", "Not found");
            ResponseEntity<Map<String, Object>> notFoundResponse = exceptionHandler.handleVitolaException(notFoundEx);
            assertEquals(HttpStatus.NOT_FOUND, notFoundResponse.getStatusCode());
            
            // Test BAD_REQUEST mappings
            String[] badRequestCodes = {"USER_DOCUMENT_IS_NULL", "USER_IS_NULL", "USER_ALREADY_EXISTS"};
            for (String code : badRequestCodes) {
                VitolaException badRequestEx = new VitolaException(code, "Bad request");
                ResponseEntity<Map<String, Object>> badRequestResponse = exceptionHandler.handleVitolaException(badRequestEx);
                assertEquals(HttpStatus.BAD_REQUEST, badRequestResponse.getStatusCode(), 
                    "Error code " + code + " should map to BAD_REQUEST");
            }
            
            // Test INTERNAL_SERVER_ERROR mapping
            VitolaException serverErrorEx = new VitolaException("RANDOM_ERROR", "Server error");
            ResponseEntity<Map<String, Object>> serverErrorResponse = exceptionHandler.handleVitolaException(serverErrorEx);
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, serverErrorResponse.getStatusCode());
        }
    }
}