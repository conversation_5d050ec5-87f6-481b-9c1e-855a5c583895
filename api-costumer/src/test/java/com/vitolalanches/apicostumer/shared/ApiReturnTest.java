package com.vitolalanches.apicostumer.shared;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("ApiReturn")
class ApiReturnTest {

    @Nested
    @DisplayName("Given I want to create a successful ApiReturn")
    class SuccessTests {
        
        @Test
        @DisplayName("When creating with result, Then should set success true")
        void shouldCreateSuccessfulReturn() {
            // Given
            String result = "Test Result";
            
            // When
            ApiReturn<String> apiReturn = new ApiReturn<>(result);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertEquals(result, apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
        
        @Test
        @DisplayName("When using static of method, Then should create successful return")
        void shouldCreateWithOfMethod() {
            // Given
            Integer result = 42;
            
            // When
            ApiReturn<Integer> apiReturn = ApiReturn.of(result);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertEquals(result, apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
        
        @Test
        @DisplayName("When creating with null result, Then should still be successful")
        void shouldCreateSuccessfulWithNullResult() {
            // When
            ApiReturn<String> apiReturn = ApiReturn.of(null);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertNull(apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
    }
    
    @Nested
    @DisplayName("Given I want to create an error ApiReturn")
    class ErrorTests {
        
        @Test
        @DisplayName("When creating with error, Then should set success false")
        void shouldCreateErrorReturn() {
            // Given
            String error = "Test Error";
            
            // When
            ApiReturn<String> apiReturn = ApiReturn.error(error);
            
            // Then
            assertFalse(apiReturn.getSuccess());
            assertNull(apiReturn.getResult());
            assertEquals(error, apiReturn.getError());
        }
        
        @Test
        @DisplayName("When using static error method, Then should create error return")
        void shouldCreateWithErrorMethod() {
            // Given
            String error = "VALIDATION_ERROR";
            
            // When
            ApiReturn<String> apiReturn = ApiReturn.error(error);
            
            // Then
            assertFalse(apiReturn.getSuccess());
            assertNull(apiReturn.getResult());
            assertEquals(error, apiReturn.getError());
        }
    }
    
    @Nested
    @DisplayName("Given I want to test setters")
    class SetterTests {
        
        @Test
        @DisplayName("When using setters, Then should update properties")
        void shouldUpdatePropertiesWithSetters() {
            // Given
            ApiReturn<String> apiReturn = new ApiReturn<>();
            
            // When
            apiReturn.setSuccess(true);
            apiReturn.setResult("New Result");
            apiReturn.setError(null);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertEquals("New Result", apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
        
        @Test
        @DisplayName("When converting success to error, Then should update correctly")
        void shouldConvertSuccessToError() {
            // Given
            ApiReturn<String> apiReturn = ApiReturn.of("Success");
            
            // When
            apiReturn.setSuccess(false);
            apiReturn.setResult(null);
            apiReturn.setError("Something went wrong");
            
            // Then
            assertFalse(apiReturn.getSuccess());
            assertNull(apiReturn.getResult());
            assertEquals("Something went wrong", apiReturn.getError());
        }
    }
    
    @Nested
    @DisplayName("Given I want to test different data types")
    class GenericTests {
        
        @Test
        @DisplayName("When using with custom object, Then should work correctly")
        void shouldWorkWithCustomObject() {
            // Given
            TestObject testObject = new TestObject("test", 123);
            
            // When
            ApiReturn<TestObject> apiReturn = ApiReturn.of(testObject);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertEquals(testObject, apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
        
        @Test
        @DisplayName("When using with boolean, Then should work correctly")
        void shouldWorkWithBoolean() {
            // Given & When
            ApiReturn<Boolean> apiReturn = ApiReturn.of(true);
            
            // Then
            assertTrue(apiReturn.getSuccess());
            assertTrue(apiReturn.getResult());
            assertNull(apiReturn.getError());
        }
    }
    
    // Classe auxiliar para testes
    private static class TestObject {
        private final String name;
        private final Integer value;
        
        public TestObject(String name, Integer value) {
            this.name = name;
            this.value = value;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return name.equals(that.name) && value.equals(that.value);
        }
        
        @Override
        public int hashCode() {
            return name.hashCode() + value.hashCode();
        }
    }
}
