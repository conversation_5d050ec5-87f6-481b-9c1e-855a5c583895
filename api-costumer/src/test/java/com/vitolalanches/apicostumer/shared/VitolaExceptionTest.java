package com.vitolalanches.apicostumer.shared;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("VitolaException")
class VitolaExceptionTest {

    @Nested
    @DisplayName("Given I want to create a VitolaException")
    class CreationTests {
        
        @Test
        @DisplayName("When creating with errorCode and message, Then should set properties correctly")
        void shouldCreateWithErrorCodeAndMessage() {
            // Given
            String errorCode = "TEST_ERROR";
            String message = "Test error message";
            
            // When
            VitolaException exception = new VitolaException(errorCode, message);
            
            // Then
            assertEquals(errorCode, exception.getErrorCode());
            assertEquals(message, exception.getMessage());
            assertNotNull(exception.getTraceId());
            assertEquals("", exception.getContextName());
            assertNull(exception.getData());
        }
        
        @Test
        @DisplayName("When creating with all parameters, Then should set all properties correctly")
        void shouldCreateWithAllParameters() {
            // Given
            String errorCode = "TEST_ERROR";
            String message = "Test error message";
            String contextName = "TestContext";
            String traceId = "test-trace-123";
            
            // When
            VitolaException exception = new VitolaException(errorCode, message, contextName, traceId);
            
            // Then
            assertEquals(errorCode, exception.getErrorCode());
            assertEquals(message, exception.getMessage());
            assertEquals(contextName, exception.getContextName());
            assertEquals(traceId, exception.getTraceId());
            assertNull(exception.getData());
        }
        
        @Test
        @DisplayName("When creating simple exception, Then should generate unique traceId")
        void shouldGenerateUniqueTraceId() {
            // Given & When
            VitolaException exception1 = new VitolaException("ERROR_1", "Message 1");
            VitolaException exception2 = new VitolaException("ERROR_2", "Message 2");
            
            // Then
            assertNotNull(exception1.getTraceId());
            assertNotNull(exception2.getTraceId());
            assertNotEquals(exception1.getTraceId(), exception2.getTraceId());
        }
    }
    
    @Nested
    @DisplayName("Given I want to add data to exception")
    class DataTests {
        
        @Test
        @DisplayName("When adding string data, Then should store and return correctly")
        void shouldStoreStringData() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            String testData = "Test data";
            
            // When
            VitolaException result = exception.withData(testData);
            
            // Then
            assertSame(exception, result); // Should return same instance
            assertEquals(testData, exception.getData());
        }
        
        @Test
        @DisplayName("When adding object data, Then should store and return correctly")
        void shouldStoreObjectData() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            TestData testData = new TestData("value1", 42);
            
            // When
            VitolaException result = exception.withData(testData);
            
            // Then
            assertSame(exception, result);
            assertEquals(testData, exception.getData());
            assertTrue(exception.getData() instanceof TestData);
        }
        
        @Test
        @DisplayName("When adding null data, Then should store null")
        void shouldStoreNullData() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            
            // When
            VitolaException result = exception.withData(null);
            
            // Then
            assertSame(exception, result);
            assertNull(exception.getData());
        }
        
        @Test
        @DisplayName("When chaining withData calls, Then should overwrite previous data")
        void shouldOverwritePreviousData() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            String firstData = "First data";
            String secondData = "Second data";
            
            // When
            exception.withData(firstData).withData(secondData);
            
            // Then
            assertEquals(secondData, exception.getData());
        }
    }
    
    @Nested
    @DisplayName("Given I want to test inheritance behavior")
    class InheritanceTests {
        
        @Test
        @DisplayName("When VitolaException is thrown, Then should be instance of RuntimeException")
        void shouldBeRuntimeException() {
            // Given
            VitolaException exception = new VitolaException("TEST_ERROR", "Test message");
            
            // Then
            assertTrue(exception instanceof RuntimeException);
        }
        
        @Test
        @DisplayName("When throwing VitolaException, Then should be catchable as RuntimeException")
        void shouldBeCatchableAsRuntimeException() {
            // Given & When & Then
            assertThrows(RuntimeException.class, () -> {
                throw new VitolaException("TEST_ERROR", "Test message");
            });
        }
        
        @Test
        @DisplayName("When throwing VitolaException, Then should preserve stack trace")
        void shouldPreserveStackTrace() {
            // Given & When
            VitolaException exception = assertThrows(VitolaException.class, () -> {
                throw new VitolaException("TEST_ERROR", "Test message");
            });
            
            // Then
            assertNotNull(exception.getStackTrace());
            assertTrue(exception.getStackTrace().length > 0);
        }
    }
    
    @Nested
    @DisplayName("Given I want to test real-world scenarios")
    class RealWorldTests {
        
        @Test
        @DisplayName("When creating user validation error, Then should have correct properties")
        void shouldCreateUserValidationError() {
            // Given
            String errorCode = "USER_DOCUMENT_IS_NULL";
            String message = "Documento é obrigatório";
            String document = "12345678900";
            
            // When
            VitolaException exception = new VitolaException(errorCode, message)
                .withData(document);
            
            // Then
            assertEquals(errorCode, exception.getErrorCode());
            assertEquals(message, exception.getMessage());
            assertEquals(document, exception.getData());
            assertNotNull(exception.getTraceId());
        }
        
        @Test
        @DisplayName("When creating service error with context, Then should have all context info")
        void shouldCreateServiceErrorWithContext() {
            // Given
            String errorCode = "DATABASE_CONNECTION_ERROR";
            String message = "Failed to connect to database";
            String contextName = "UserService";
            String traceId = "req-123-456";
            
            // When
            VitolaException exception = new VitolaException(errorCode, message, contextName, traceId);
            
            // Then
            assertEquals(errorCode, exception.getErrorCode());
            assertEquals(message, exception.getMessage());
            assertEquals(contextName, exception.getContextName());
            assertEquals(traceId, exception.getTraceId());
        }
    }
    
    // Classe auxiliar para testes
    private static class TestData {
        private final String stringValue;
        private final Integer intValue;
        
        public TestData(String stringValue, Integer intValue) {
            this.stringValue = stringValue;
            this.intValue = intValue;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestData testData = (TestData) obj;
            return stringValue.equals(testData.stringValue) && intValue.equals(testData.intValue);
        }
        
        @Override
        public int hashCode() {
            return stringValue.hashCode() + intValue.hashCode();
        }
    }
}