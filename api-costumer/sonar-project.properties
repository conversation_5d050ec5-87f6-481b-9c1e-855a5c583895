sonar.projectKey=vitola-lanches_api-costumer
sonar.organization=vitola-lanches
sonar.projectName=api-costumer

# Source directories
sonar.sources=src/main/java
sonar.tests=src/test/java

# Compiled classes directory
sonar.java.binaries=target/classes
sonar.java.test.binaries=target/test-classes

# Coverage reports
sonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml

# Exclusions
sonar.exclusions=**/ApiCostumerApplication.java,**/config/**,**/dto/**,**/entities/**,**/business/users/control/**,**/business/users/controllers/**,**/shared/BaseController**
sonar.coverage.exclusions=**/ApiCostumerApplication.java,**/config/**,**/dto/**,**/entities/**,**/business/users/control/**,**/business/users/controllers/**,**/shared/BaseController**
