apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-costumer
  namespace: vitola-lanches
  labels:
    app: api-costumer
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-costumer
  template:
    metadata:
      labels:
        app: api-costumer
        version: v1
    spec:
      containers:
      - name: api-costumer
        image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/api-costumer:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        - name: SPRING_DATASOURCE_URL
          value: "**********************************************"
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        - name: SPRING_REDIS_PORT
          value: "6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30

---
apiVersion: v1
kind: Service
metadata:
  name: api-costumer-service
  namespace: vitola-lanches
  labels:
    app: api-costumer
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: api-costumer

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-costumer-ingress
  namespace: vitola-lanches
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api-costumer.vitola-lanches.com
    secretName: api-costumer-tls
  rules:
  - host: api-costumer.vitola-lanches.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-costumer-service
            port:
              number: 8080

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-costumer-hpa
  namespace: vitola-lanches
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-costumer
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
