<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.5">
  <diagram name="Page-1" id="SLoXg-f9CZIAaR6V7906">
    <mxGraphModel dx="2046" dy="867" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="4681" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="YH3uHMh_-X-k8-K38k-l-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="YH3uHMh_-X-k8-K38k-l-17" target="YH3uHMh_-X-k8-K38k-l-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="YH3uHMh_-X-k8-K38k-l-17" target="YH3uHMh_-X-k8-K38k-l-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-17" value="&lt;div&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;font style=&quot;&quot;&gt;api-costumer&lt;/font&gt;&lt;br&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;br&gt;&lt;/font&gt;&lt;/div&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=api" parent="1" vertex="1">
          <mxGeometry x="850" y="601" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-19" value="" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/Cache_Redis_Product.svg;" parent="1" vertex="1">
          <mxGeometry x="677.5" y="604" width="50" height="42" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-21" value="" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.mysql_database;" parent="1" vertex="1">
          <mxGeometry x="856.25" y="741" width="37.5" height="50" as="geometry" />
        </mxCell>
        <mxCell id="WR3czjm8chM07-9-S7Sv-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.1;exitY=0.2;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="YH3uHMh_-X-k8-K38k-l-25" target="YH3uHMh_-X-k8-K38k-l-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WR3czjm8chM07-9-S7Sv-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="YH3uHMh_-X-k8-K38k-l-25" target="YH3uHMh_-X-k8-K38k-l-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="u40C3HnWVMl9SQJvC62k-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="YH3uHMh_-X-k8-K38k-l-25" target="YH3uHMh_-X-k8-K38k-l-33">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-25" value="&lt;div&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;api-orders&lt;/font&gt;&lt;/div&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=api" parent="1" vertex="1">
          <mxGeometry x="850" y="460" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-33" value="&lt;div&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;api-payments&lt;/font&gt;&lt;/div&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=api" parent="1" vertex="1">
          <mxGeometry x="850" y="260" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-30" value="" style="dashed=0;outlineConnect=0;html=1;align=center;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;shape=mxgraph.weblogos.mongodb" parent="1" vertex="1">
          <mxGeometry x="680.65" y="269.99999999999994" width="41.2" height="86.2" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-47" value="Amazon EKS" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#ffffff;fillColor=#232F3E;dashed=0;verticalLabelPosition=middle;verticalAlign=bottom;align=center;html=1;whiteSpace=wrap;fontSize=10;fontStyle=1;spacing=3;shape=mxgraph.aws4.productIcon;prIcon=mxgraph.aws4.eks;" parent="1" vertex="1">
          <mxGeometry x="560" y="160" width="50" height="77.95" as="geometry" />
        </mxCell>
        <mxCell id="WR3czjm8chM07-9-S7Sv-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="fzyHF1DzcqaIj5i1yFd1-1" target="YH3uHMh_-X-k8-K38k-l-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fzyHF1DzcqaIj5i1yFd1-1" value="Cliente" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.user;" parent="1" vertex="1">
          <mxGeometry x="677.5" y="459" width="47.5" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fzyHF1DzcqaIj5i1yFd1-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="fzyHF1DzcqaIj5i1yFd1-5" target="YH3uHMh_-X-k8-K38k-l-71" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fzyHF1DzcqaIj5i1yFd1-5" value="Operador" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.user;" parent="1" vertex="1">
          <mxGeometry x="1330" y="459" width="47.5" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eB3ktSCFNJmir2KBiJFd-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="YH3uHMh_-X-k8-K38k-l-71" target="YH3uHMh_-X-k8-K38k-l-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="YH3uHMh_-X-k8-K38k-l-71" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;api-kitchen&lt;/font&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=api" parent="1" vertex="1">
          <mxGeometry x="1102.5" y="459.9999999999999" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="eB3ktSCFNJmir2KBiJFd-5" value="Comunicação via RestApi&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="970" y="450" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="eB3ktSCFNJmir2KBiJFd-6" value="Comunicação via RestApi&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="890" y="380" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="eB3ktSCFNJmir2KBiJFd-7" value="&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 246, 246));&quot;&gt;Mercado Pago&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 246, 246));&quot;&gt;&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=light-dark(#E51400,#FF3E3E);strokeColor=light-dark(#B20000, #e9dddd);points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=api;fontColor=#ffffff;gradientColor=default;" parent="1" vertex="1">
          <mxGeometry x="1120" y="260" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="u40C3HnWVMl9SQJvC62k-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.96;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;startArrow=classic;startFill=1;" edge="1" parent="1" source="eB3ktSCFNJmir2KBiJFd-7" target="YH3uHMh_-X-k8-K38k-l-33">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
