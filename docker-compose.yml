version: '3.8'

services:
  # ===========================================
  # DATABASES & INFRASTRUCTURE
  # ===========================================
  
  # MongoDB para api-orders e api-payments
  mongodb:
    image: mongo:latest
    container_name: vitola-mongodb
    restart: always
    ports:
      - '27017:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - mongodb_data:/data/db
      - ./api-orders/mongod.conf:/etc/mongo/mongod.conf:ro
    command: ['mongod', '--config', '/etc/mongo/mongod.conf']
    networks:
      - vitola-network
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 10s
      retries: 5

  # MySQL para api-costumer
  mysql:
    image: mysql:8.0
    container_name: vitola-mysql
    restart: always
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: root-password
      MYSQL_DATABASE: vitola_lanches
      MYSQL_USER: admin
      MYSQL_PASSWORD: admin
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - vitola-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis compartilhado
  redis:
    image: redis:7.2-alpine
    container_name: vitola-redis
    restart: always
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - vitola-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===========================================
  # MICROSERVICES - COMENTADOS PARA DESENVOLVIMENTO LOCAL
  # ===========================================
  #
  # As APIs devem ser executadas localmente usando:
  # - cd api-orders && yarn start:dev
  # - cd api-payments && yarn start:dev
  # - cd api-kitchen && yarn start:dev
  # - cd api-costumer && ./mvnw spring-boot:run
  #
  # Descomente as seções abaixo se quiser rodar as APIs no Docker

  # ===========================================
  # DEVELOPMENT TOOLS
  # ===========================================

  # SonarQube para análise de código
  sonarqube:
    image: sonarqube:10.6-community
    container_name: vitola-sonarqube
    restart: always
    ports:
      - '9000:9000'
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
      - SONAR_JDBC_URL=*******************************************
      - SONAR_JDBC_USERNAME=sonar
      - SONAR_JDBC_PASSWORD=sonar
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
    depends_on:
      sonar-postgres:
        condition: service_healthy
    networks:
      - vitola-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9000/api/system/status | grep -q UP || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL para SonarQube
  sonar-postgres:
    image: postgres:15-alpine
    container_name: vitola-sonar-postgres
    restart: always
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - sonar_postgres_data:/var/lib/postgresql/data
    networks:
      - vitola-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar"]
      interval: 10s
      timeout: 5s
      retries: 5

# ===========================================
# NETWORKS & VOLUMES
# ===========================================

networks:
  vitola-network:
    driver: bridge
    name: vitola-lanches-network

volumes:
  mongodb_data:
    name: vitola-mongodb-data
  mysql_data:
    name: vitola-mysql-data
  redis_data:
    name: vitola-redis-data
  sonarqube_data:
    name: vitola-sonarqube-data
  sonarqube_extensions:
    name: vitola-sonarqube-extensions
  sonarqube_logs:
    name: vitola-sonarqube-logs
  sonar_postgres_data:
    name: vitola-sonar-postgres-data
