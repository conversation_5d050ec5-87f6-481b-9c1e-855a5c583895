# 🍔 Vitola Lanches - Microservices Platform

Sistema completo de lanchonete baseado em arquitetura de microserviços, desenvolvido para demonstrar boas práticas de desenvolvimento, CI/CD e deploy em nuvem.

## 🏗️ Arquitetura

```
┌─────────────────┐    REST API    ┌─────────────────┐
│   API-ORDERS    │ ──────────────► │   API-PAYMENTS  │
│  (Order Service)│                 │ (Payment Service)│
│     :3001       │ ◄────────────── │     :3002       │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │ REST API                          │
         ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│   API-KITCHEN   │                 │   API-COSTUMER  │
│(Production Serv)│                 │(Customer Service)│
│     :3003       │                 │     :8080       │
└─────────────────┘                 └─────────────────┘
```

### 🎯 Serviços

| Serviço | Tecnologia | Porta | Responsabilidade |
|---------|------------|-------|------------------|
| **api-orders** | NestJS + MongoDB | 3001 | Orquestração de pedidos |
| **api-payments** | NestJS + MongoDB | 3002 | Processamento de pagamentos |
| **api-kitchen** | NestJS (REST Client) | 3003 | Interface da cozinha |
| **api-costumer** | Spring Boot + MySQL | 8080 | Gestão de clientes |

### 🗄️ Bancos de Dados

- **MongoDB**: Usado por `api-orders` e `api-payments`
- **MySQL**: Usado por `api-costumer`
- **Redis**: Cache compartilhado por todos os serviços

## 🚀 Início Rápido

### 1. Pré-requisitos

```bash
# Docker e Docker Compose
docker --version
docker-compose --version

# Git
git --version
```

### 2. Clonar e Configurar

```bash
# Clonar o repositório
git clone <repository-url>
cd vitola-lanches

# Configurar variáveis de ambiente
cp .env.example .env
# Edite o .env com suas configurações
```

### 3. Iniciar Todo o Sistema

```bash
# Dar permissão ao script (apenas primeira vez)
chmod +x start-vitola.sh

# Iniciar todos os serviços
./start-vitola.sh start
```

### 4. Verificar Status

```bash
# Ver status dos serviços
./start-vitola.sh status

# Testar conectividade
./start-vitola.sh test
```

## 🌐 Endpoints Disponíveis

### APIs Principais
- **API Orders**: http://localhost:3001
- **API Payments**: http://localhost:3002  
- **API Kitchen**: http://localhost:3003
- **API Costumer**: http://localhost:8080

### Infraestrutura
- **SonarQube**: http://localhost:9000
- **MongoDB**: localhost:27017
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 🛠️ Comandos Úteis

### Desenvolvimento Local (Recomendado)
```bash
# Iniciar tudo automaticamente (infraestrutura + APIs)
./start-apis-local.sh

# Parar tudo
./stop-apis-local.sh

# Ver logs das APIs
tail -f logs/api-orders.log
tail -f logs/api-costumer.log

# Testar conectividade
./test-apis.sh
```

### Gerenciamento da Infraestrutura
```bash
# Iniciar apenas infraestrutura (MongoDB, MySQL, Redis, SonarQube)
./start-vitola.sh dev

# Parar infraestrutura
./start-vitola.sh stop

# Ver status da infraestrutura
./start-vitola.sh status

# Ver logs da infraestrutura
./start-vitola.sh logs
```

### Docker Compose (Para produção/testes)
```bash
# Reconstruir imagens
./start-vitola.sh build

# Limpar tudo (containers, volumes, imagens)
./start-vitola.sh clean
```

### Docker Compose Direto
```bash
# Iniciar serviços específicos
docker-compose up -d mongodb mysql redis

# Ver logs em tempo real
docker-compose logs -f api-orders

# Executar comandos dentro de containers
docker-compose exec api-orders yarn test
```

## 🧪 Desenvolvimento Local

### Opção 1: Desenvolvimento Automático (Recomendado)
```bash
# Inicia infraestrutura + todas as APIs automaticamente
chmod +x start-apis-local.sh stop-apis-local.sh
./start-apis-local.sh

# Para parar tudo
./stop-apis-local.sh
```

### Opção 2: Infraestrutura no Docker + APIs Manual
```bash
# 1. Iniciar apenas infraestrutura
./start-vitola.sh dev

# 2. Rodar APIs individualmente (em terminais separados)
cd api-orders && yarn start:dev      # Porta 3001
cd api-payments && yarn start:dev    # Porta 3002
cd api-kitchen && yarn start:dev     # Porta 3003
cd api-costumer && ./mvnw spring-boot:run  # Porta 8080
```

### Opção 3: Tudo no Docker (Para testes de produção)
```bash
# Descomente as seções das APIs no docker-compose.yml
./start-vitola.sh start
```

## 📊 Qualidade de Código

### SonarQube
Acesse http://localhost:9000 após iniciar os serviços.

**Credenciais padrão:**
- Usuário: `admin`
- Senha: `admin`

### Executar Análise
```bash
# Para APIs NestJS
cd api-orders
yarn run sonar

# Para API Spring Boot
cd api-costumer
./mvnw sonar:sonar
```

## 🔧 Configuração

### Variáveis de Ambiente (.env)

```env
# Mercado Pago (obrigatório para pagamentos)
MERCADO_PAGO_ACCESSTOKEN=TEST-your-token-here

# Bancos de dados
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password
MYSQL_ROOT_PASSWORD=root-password
MYSQL_USER=admin
MYSQL_PASSWORD=admin-pwd

# Ambiente
NODE_ENV=development
SPRING_PROFILES_ACTIVE=development
```

### Portas Utilizadas

| Porta | Serviço | Descrição |
|-------|---------|-----------|
| 3001 | api-orders | API de Pedidos |
| 3002 | api-payments | API de Pagamentos |
| 3003 | api-kitchen | API da Cozinha |
| 8080 | api-costumer | API de Clientes |
| 27017 | MongoDB | Banco NoSQL |
| 3306 | MySQL | Banco Relacional |
| 6379 | Redis | Cache |
| 9000 | SonarQube | Qualidade de Código |

## 🚀 Deploy em Produção

### AWS EKS
Cada API possui configuração para deploy no AWS EKS via GitHub Actions.

```bash
# Configurar secrets no GitHub
# Ver: api-costumer/GITHUB_SECRETS.md

# Deploy automático via push
git push origin main
```

### Docker Swarm
```bash
# Converter para stack do Swarm
docker-compose config > docker-stack.yml
docker stack deploy -c docker-stack.yml vitola-lanches
```

## 🔍 Troubleshooting

### Problemas Comuns

**Erro de incompatibilidade Node.js:**
```bash
# Se aparecer erro "The engine "node" is incompatible"
chmod +x fix-docker-build.sh
./fix-docker-build.sh
```

**Porta já em uso:**
```bash
# Verificar processos usando as portas
lsof -i :3001
lsof -i :3306

# Parar serviços conflitantes
./start-vitola.sh stop
```

**Problemas de conectividade:**
```bash
# Verificar logs
./start-vitola.sh logs

# Testar conectividade
./start-vitola.sh test

# Reiniciar serviços
./start-vitola.sh restart
```

**Problemas de build:**
```bash
# Limpar e reconstruir
./start-vitola.sh clean
./start-vitola.sh build

# Ou usar o script de correção
./fix-docker-build.sh
```

### Logs Úteis
```bash
# Ver logs de todos os serviços
docker-compose logs

# Ver logs de um serviço específico
docker-compose logs api-orders

# Seguir logs em tempo real
docker-compose logs -f api-payments
```

## 📚 Documentação Adicional

- [API Orders](./api-orders/README.md)
- [API Payments](./api-payments/README.md)
- [API Kitchen](./api-kitchen/README.md)
- [API Costumer](./api-costumer/README.md)
- [GitHub Actions](./actions/README.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 👥 Equipe

- **Desenvolvimento**: Vitola Lanches Team
- **Arquitetura**: Microservices Pattern
- **Deploy**: AWS EKS + GitHub Actions
