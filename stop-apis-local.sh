#!/bin/bash

# Script para parar todas as APIs locais e infraestrutura

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠️  $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] ℹ️  $1${NC}"
}

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🛑 PARANDO APIS 🛑                        ║"
echo "║                   Vitola Lanches APIs                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Parar APIs pelos PIDs salvos
stop_api_by_pid() {
    local api_name=$1
    local pid_file="logs/${api_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            info "Parando $api_name (PID: $pid)..."
            kill -TERM $pid 2>/dev/null || kill -9 $pid 2>/dev/null || true
            sleep 2
            
            if ps -p $pid > /dev/null 2>&1; then
                warn "$api_name ainda rodando, forçando parada..."
                kill -9 $pid 2>/dev/null || true
            fi
            
            log "$api_name parado"
        else
            warn "$api_name não estava rodando (PID $pid não encontrado)"
        fi
        rm -f "$pid_file"
    else
        warn "Arquivo PID não encontrado para $api_name"
    fi
}

# Parar APIs pelas portas
stop_api_by_port() {
    local api_name=$1
    local port=$2
    
    info "Verificando $api_name na porta $port..."
    
    if lsof -ti:$port > /dev/null 2>&1; then
        info "Parando $api_name na porta $port..."
        kill -9 $(lsof -ti:$port) 2>/dev/null || true
        sleep 1
        
        if lsof -ti:$port > /dev/null 2>&1; then
            warn "$api_name ainda rodando na porta $port"
        else
            log "$api_name parado (porta $port liberada)"
        fi
    else
        info "$api_name não estava rodando na porta $port"
    fi
}

# Parar APIs pelos PIDs primeiro
info "Parando APIs pelos PIDs salvos..."
stop_api_by_pid "api-orders"
stop_api_by_pid "api-payments"
stop_api_by_pid "api-kitchen"
stop_api_by_pid "api-costumer"

# Parar APIs pelas portas como backup
info "Verificando portas e parando processos restantes..."
stop_api_by_port "API Orders" "3001"
stop_api_by_port "API Payments" "3002"
stop_api_by_port "API Kitchen" "3003"
stop_api_by_port "API Costumer" "8080"

# Parar infraestrutura Docker
info "Parando infraestrutura Docker..."
./start-vitola.sh stop

# Limpar arquivos de log se solicitado
if [ "$1" = "--clean-logs" ]; then
    info "Limpando logs..."
    rm -rf logs/
    log "Logs limpos"
fi

echo ""
log "🎉 Todas as APIs e infraestrutura foram paradas!"
echo ""
echo -e "${BLUE}💡 Para reiniciar:${NC}"
echo -e "${BLUE}  • Tudo: ./start-apis-local.sh${NC}"
echo -e "${BLUE}  • Só infraestrutura: ./start-vitola.sh dev${NC}"
echo -e "${BLUE}  • APIs individuais: cd [api] && yarn start:dev${NC}"
echo ""
echo -e "${YELLOW}🧹 Para limpar logs na próxima parada:${NC}"
echo -e "${YELLOW}  ./stop-apis-local.sh --clean-logs${NC}"
