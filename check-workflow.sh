#!/bin/bash

# Script para verificar se o workflow reutilizável está acessível

echo "🔍 Verificando workflow reutilizável..."
echo ""

# Verificar se o repositório actions existe
echo "1. Verificando se o repositório vitola-lanches/actions existe..."
if curl -s -f https://api.github.com/repos/vitola-lanches/actions > /dev/null; then
    echo "✅ Repositório vitola-lanches/actions existe"
else
    echo "❌ Repositório vitola-lanches/actions não encontrado ou não é público"
    echo "   Verifique se o repositório existe e é público"
    exit 1
fi

echo ""

# Verificar se o workflow existe
echo "2. Verificando se o workflow deploy-java.yml existe..."
if curl -s -f https://api.github.com/repos/vitola-lanches/actions/contents/.github/workflows/deploy-java.yml > /dev/null; then
    echo "✅ Workflow deploy-java.yml encontrado"
else
    echo "❌ Workflow deploy-java.yml não encontrado"
    echo "   Verifique se o arquivo está em: .github/workflows/deploy-java.yml"
    echo "   E se foi commitado na branch main"
    exit 1
fi

echo ""

# Verificar conteúdo do workflow
echo "3. Verificando conteúdo do workflow..."
workflow_content=$(curl -s https://api.github.com/repos/vitola-lanches/actions/contents/.github/workflows/deploy-java.yml)

if echo "$workflow_content" | grep -q "workflow_call"; then
    echo "✅ Workflow está configurado como reutilizável (workflow_call)"
else
    echo "❌ Workflow não está configurado como reutilizável"
    echo "   Certifique-se que o workflow tem 'on: workflow_call'"
fi

echo ""
echo "🎯 Diagnóstico completo!"
echo ""
echo "Se todos os itens estão ✅, o problema pode ser:"
echo "1. Permissões do repositório actions"
echo "2. O workflow não foi pushed para a branch main"
echo "3. Cache do GitHub Actions (aguarde alguns minutos)"
