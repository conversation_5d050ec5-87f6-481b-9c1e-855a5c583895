# API Orders - Vitola Lanches

API de gerenciamento de pedidos para o sistema Vitola Lanches, desenvolvida com NestJS, MongoDB e integração com Mercado Pago para pagamentos.

## 🚀 Tecnologias

- **NestJS** - Framework Node.js para construção de aplicações server-side
- **TypeScript** - Linguagem de programação
- **MongoDB** - Banco de dados NoSQL
- **Mongoose** - ODM para MongoDB
- **Redis** - Cache e sessões
- **Swagger** - Documentação da API
- **Jest** - Framework de testes
- **Docker** - Containerização
- **SonarQube** - Análise de qualidade de código

## 📋 Funcionalidades

### Módulos Principais

- **Orders (Pedidos)**: Criação, consulta e gerenciamento de status de pedidos
- **Products (Produtos)**: Cadastro e consulta de produtos por categoria
- **Users (Usuários)**: Gerenciamento de usuários do sistema
- **Payments (Pagamentos)**: Integração com Mercado Pago para processamento de pagamentos

### Categorias de Produtos

- `LANCHE` - Lanches e hambúrguers
- `BEBIDA` - Bebidas em geral
- `ACOMPANHAMENTO` - Acompanhamentos e sides
- `SOBREMESA` - Sobremesas

### Status de Pedidos

- `RECEIVED` - Pedido recebido
- `PREPARING` - Em preparação
- `READY` - Pronto para retirada
- `FINISHED` - Finalizado

## 🛠️ Instalação e Configuração

### Pré-requisitos

- Node.js 18+
- Yarn
- Docker e Docker Compose
- MongoDB
- Redis

### Instalação

```bash
# Clone o repositório
git clone <repository-url>
cd api-orders

# Instale as dependências
yarn install

# Configure as variáveis de ambiente
cp .env.example .env
```

### Variáveis de Ambiente

```env
# Database
MONGO_URI=mongodb://localhost:27017/vitola-orders
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password

# Payment
MERCADO_PAGO_ACCESSTOKEN=your-mercado-pago-token

# External APIs
PAYMENT_API_URL=http://localhost:3001
COSTUMER_API_URL=http://localhost:3002

# Application
PORT=3000
NODE_ENV=development
```

## 🚀 Executando a Aplicação

### Desenvolvimento

```bash
# Modo desenvolvimento
yarn start:dev

# Modo debug
yarn start:debug
```

### Produção

```bash
# Build da aplicação
yarn build

# Executar em produção
yarn start:prod
```

### Docker

```bash
# Executar com Docker Compose
docker-compose up --build

# Ou usar o script de build
./build_app.sh
```

## 🧪 Testes

```bash
# Executar todos os testes
yarn test

# Testes em modo watch
yarn test:watch

# Testes de cobertura
yarn test:cov

# Testes e2e
yarn test:e2e
```

## 📚 Documentação da API

A documentação da API está disponível via Swagger:

- **Local**: http://localhost:3000/api
- **Produção**: Conforme configuração do ambiente

### Principais Endpoints

#### Pedidos
- `POST /orders` - Criar novo pedido
- `GET /orders` - Listar pedidos
- `PUT /orders/:id/status` - Atualizar status do pedido
- `PUT /orders/:id/confirm` - Confirmar pedido

#### Produtos
- `GET /products` - Listar todos os produtos
- `GET /products/category/:category` - Listar produtos por categoria
- `POST /products` - Criar novo produto

#### Usuários
- `POST /users` - Criar usuário
- `GET /users/:document/infos` - Buscar usuário por documento

#### Pagamentos
- `POST /payments` - Processar pagamento

## 🏗️ Arquitetura

O projeto segue os princípios da Clean Architecture e Domain-Driven Design:

```
src/
├── modules/
│   ├── orders/          # Módulo de pedidos
│   │   ├── controllers/ # Controllers REST
│   │   ├── dto/         # Data Transfer Objects
│   │   ├── repositories/# Repositórios
│   │   ├── services/    # Serviços de domínio
│   │   └── schema/      # Schemas MongoDB
│   ├── products/        # Módulo de produtos
│   ├── users/           # Módulo de usuários
│   ├── payments/        # Módulo de pagamentos
│   └── shared/          # Módulos compartilhados
└── main.ts             # Ponto de entrada da aplicação
```

## 🔧 Qualidade de Código

### SonarQube

```bash
# Executar análise do SonarQube
./run_sonnar.sh
```

### Linting e Formatação

```bash
# Executar ESLint
yarn lint

# Formatar código
yarn format
```

## 🚀 CI/CD

O projeto utiliza GitHub Actions para CI/CD:

- **Build**: Compilação e testes automatizados
- **Quality Gate**: Análise de qualidade com SonarQube
- **Deploy**: Deploy automático para AWS EKS

### Pipeline

1. Testes unitários e de integração
2. Análise de qualidade (SonarQube)
3. Build da imagem Docker
4. Deploy para ambiente de produção

## 🐳 Docker

### Serviços Disponíveis

- **app**: Aplicação principal (porta 3000)
- **mongodb**: Banco de dados MongoDB (porta 27017)
- **redis**: Cache Redis (porta 6379)
- **sonarqube**: Análise de código (porta 9000)

## 📝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença UNLICENSED - veja o arquivo [package.json](package.json) para detalhes.

## 🤝 Suporte

Para suporte e dúvidas, entre em contato com a equipe de desenvolvimento.
