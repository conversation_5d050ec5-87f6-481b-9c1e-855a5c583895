sonar.projectKey=vitola-lanches_api-orders
sonar.organization=vitola-lanches
sonar.projectName=api-orders
sonar.projectVersion=1.0

# sonar.host.url=http://localhost:9000
# Configurações de Código Fonte
sonar.sources=src
sonar.tests=test
sonar.test.inclusions=test/**/*.spec.ts,test/**/*.e2e-spec.ts

# Exclusões
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/build/**,**/*.d.ts
sonar.test.exclusions=**/node_modules/**,**/dist/**,**/coverage/**

# Configurações de Cobertura
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts,**/main.ts,**/test/**

# Configurações de Duplicação
sonar.cpd.exclusions=**/*.spec.ts,**/*.e2e-spec.ts,**/test/**


sonar.typescript.tsconfigPath=tsconfig.json
sonar.sourceEncoding=UTF-8
sonar.language=ts

sonar.qualitygate.wait=true