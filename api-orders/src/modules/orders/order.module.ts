import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentModule } from '../payments/payment.module';
import MongooseConnection from '../shared/database/connection/mongoose.connection';
import { OrderManagementServicePortToken, OrderRepositoryPortToken } from './constants/order.constants';
import { OrderController } from './controllers/order.controller';
import { OrderRepository } from './repositories/order.repository';
import { Order, OrderSchema } from './schema/order.schema';
import { OrderManagementService } from './services/order-management.service';

const MODELS = MongooseModule.forFeature([{ name: Order.name, schema: OrderSchema }]);

@Module({
  imports: [MODELS, MongooseConnection, PaymentModule],
  providers: [
    { provide: OrderRepositoryPortToken, useClass: OrderRepository },
    { provide: OrderManagementServicePortToken, useClass: OrderManagementService },
  ],
  controllers: [OrderController],
})
export class OrderModule {}
