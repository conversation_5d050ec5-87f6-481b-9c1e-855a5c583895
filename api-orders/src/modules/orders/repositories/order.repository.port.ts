import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderStatus } from '../models/order-status';
import { Order } from '../schema/order.schema';

export interface OrderRepositoryPort {
  confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean>;
  create(order: OrderDto): Promise<string>;
  findOrders(): Promise<Order[]>;
  changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean>;
}
