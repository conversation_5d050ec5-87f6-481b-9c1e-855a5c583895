import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { objectIdFromStringValue } from '../../shared/database/helpers/generate-objectId';
import { UpdateVerifier } from '../../shared/database/helpers/update-verifier';
import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderStatus } from '../models/order-status';
import { Order } from '../schema/order.schema';
import { OrderRepositoryPort } from './order.repository.port';

@Injectable()
export class OrderRepository implements OrderRepositoryPort {
  constructor(@InjectModel('Order') private readonly orderModel: Model<Order>) {}

  async confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    const updated = await this.orderModel.updateOne(
      {
        _id: objectIdFromStringValue(orderId),
      },
      {
        $set: {
          status: OrderStatus.PREPARING,
          updateDate: new Date(),
          paymentInformation: {
            paymentProof: orderConfirmed.paymentProof,
            totalPaid: orderConfirmed.totalPaid,
            confirmed: orderConfirmed.confirmed,
            updateDate: new Date(),
          },
        },
      },
    );

    return UpdateVerifier.wasUpdated(updated);
  }

  async create(order: OrderDto): Promise<string> {
    const createdOrder = new this.orderModel(order);

    await this.orderModel.create(createdOrder);

    return createdOrder._id;
  }

  async findOrders(): Promise<Order[]> {
    return await this.orderModel.find().sort({ createDate: -1 });
  }

  async changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean> {
    const updated = await this.orderModel.updateOne(
      {
        _id: objectIdFromStringValue(orderId),
      },
      {
        $set: {
          status: status,
        },
      },
    );

    return UpdateVerifier.wasUpdated(updated);
  }
}
