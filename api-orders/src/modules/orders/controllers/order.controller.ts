import { Body, Controller, Get, Inject, Param, Post, Put } from '@nestjs/common';
import { ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrderDtoMockDocumentation, OrderManagementServicePortToken, OrderResponseDtoMockDocumentation } from '../constants/order.constants';
import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderResponseDto } from '../dto/order-status.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderStatus } from '../models/order-status';
import { OrderManagementServicePort } from '../services/order-management.service.port';
import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';

@ApiTags('Pedidos')
@Controller('orders')
export class OrderController {
  constructor(@Inject(OrderManagementServicePortToken) private readonly orderService: OrderManagementServicePort) {}

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Lista de pedidos encontrada com sucesso',
    schema: {
      example: {
        success: true,
        return: [OrderResponseDtoMockDocumentation],
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Nenhum pedido listado',
    schema: {
      example: {
        success: false,
        return: [],
      },
    },
  })
  async findOrdersStatus(): Promise<Array<OrderResponseDto>> {
    return await this.orderService.findOrders();
  }

  @Post('issue')
  @ApiResponse({ status: 201, description: 'Pedido criado com sucesso', schema: { example: { success: true, return: OrderDtoMockDocumentation } } })
  async issueOrder(@Body() order: OrderDto): Promise<OrderDto> {
    return await this.orderService.issueOrder(order);
  }

  @Put(':orderId/confirm')
  @ApiResponse({ status: 200, description: 'Pedido confirmado com sucesso', schema: { example: { success: true, return: true } } })
  @ApiResponse({
    status: 500,
    description: 'Id do pedido não informado',
    schema: {
      example: { success: false, return: { errorCode: 'ORDERID_IS_REQUIRED', message: 'Id do pedido inválido' } },
    },
  })
  async confirmeOrder(@Param('orderId') orderId: string, @Body() orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    return await this.orderService.confirmOrder(orderId, orderConfirmed);
  }

  @Put(':orderId/status/:status')
  @ApiParam({
    name: 'orderId',
    description: 'Id do pedido',
    example: generateNewObjectId(),
  })
  @ApiParam({
    name: 'status',
    description: 'Status do pedido',
    example: OrderStatus.READY,
    enum: OrderStatus,
  })
  @ApiResponse({ status: 200, description: 'Atualiza status do pedido', schema: { example: { success: true, return: true } } })
  @ApiResponse({
    status: 500,
    description: 'Id do pedido não informado',
    content: {
      'aaplication/json': {
        examples: {
          id_invalido: {
            summary: 'Id do pedido não informado',
            value: { success: false, return: { errorCode: 'ORDERID_IS_REQUIRED', message: 'Id do pedido inválido' } },
          },
          status_invalido: {
            summary: 'Status do pedido não informado',
            value: { success: false, return: { errorCode: 'STATUS_IS_REQUIRED', message: 'Status do pedido inválido' } },
          },
        },
      },
    },
  })
  async changeOrderStatus(@Param('orderId') orderId: string, @Param('status') status: OrderStatus): Promise<boolean> {
    return await this.orderService.changeOrderStatus(orderId, status);
  }
}
