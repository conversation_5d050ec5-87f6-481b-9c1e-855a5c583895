import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';
import { OrderResponseDto } from '../dto/order-status.dto';
import { OrderDto } from '../dto/order.dto';
import { PaymentMethod } from '../models/order-payment-method';
import { OrderStatus } from '../models/order-status';

export const OrderRepositoryPortToken = Symbol('OrderRepositoryPort');
export const OrderManagementServicePortToken = Symbol('OrderManagementServicePort');

export const OrderDtoMockDocumentation: OrderDto = {
  id: `${generateNewObjectId()}`,
  document: '12345678900',
  items: [
    {
      name: 'Misto quente',
      description: 'Misto quente guloso',
      quantity: 1,
      unitPrice: 10,
    },
  ],
  paymentInformation: {
    paymentMethod: PaymentMethod.QRCODEPIX,
    paymentUrl: 'url-de-pagamento.com.br',
    qrCodeUrl: 'qr-code-url-de-pagamento.com.br',
    paymentProof: 'comprovante',
    totalPaid: 10,
    confirmed: true,
    createDate: new Date(),
    updateDate: new Date(),
  },
  total: 10,
  orderNumber: 987,
} as OrderDto;

export const OrderResponseDtoMockDocumentation: OrderResponseDto = {
  id: `${generateNewObjectId()}`,
  orderNumber: 123,
  orderStatus: OrderStatus.COMPLETED,
  receivedDate: new Date(),
  lastUpdateDate: new Date(),
};
