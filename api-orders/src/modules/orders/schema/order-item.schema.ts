import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({
  versionKey: false,
  _id: false,
})
export class OrderItem extends Document {
  @Prop()
  name: string;

  @Prop()
  description: string;

  @Prop()
  quantity: number;

  @Prop({
    type: Types.Decimal128,
  })
  unitPrice: number;
}

export const OrderItemSchema = SchemaFactory.createForClass(OrderItem);
