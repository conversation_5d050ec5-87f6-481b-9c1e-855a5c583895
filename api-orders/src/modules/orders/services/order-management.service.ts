import { Inject, Injectable, Logger } from '@nestjs/common';
import { PaymentService } from '../../payments/services/payment.service';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderRepositoryPortToken } from '../constants/order.constants';
import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderResponseDto } from '../dto/order-status.dto';
import { OrderDto } from '../dto/order.dto';
import { PaymentInformation } from '../models/order-payment-information';
import { OrderStatus } from '../models/order-status';
import { OrderRepositoryPort } from '../repositories/order.repository.port';
import { OrderManagementServicePort } from './order-management.service.port';

@Injectable()
export class OrderManagementService implements OrderManagementServicePort {
  private readonly logger = new Logger(OrderManagementService.name);

  constructor(
    @Inject(OrderRepositoryPortToken) private readonly orderRepository: OrderRepositoryPort,
    private readonly paymentService: PaymentService,
  ) {}

  private async create(order: OrderDto): Promise<string> {
    return await this.orderRepository.create(order);
  }

  async issueOrder(order: OrderDto): Promise<OrderDto> {
    if (!order) {
      throw VitolaException.ofValidation('ORDER_NOT_FOUND', 'Escolha ao menos 1 item para criar o pedido.');
    }

    OrderDto.validate(order);

    const paymentInfo = await this.paymentService.issuePayment(order);

    try {
      order.paymentInformation = PaymentInformation.of(paymentInfo);
      order.id = await this.create(order);
    } catch (exception) {
      this.logger.log(`Erro durante criação do pedido: ${exception}`);
      throw VitolaException.ofValidation('ORDER_CREATION_ERROR', 'Ocorreu um erro ao criar pedido.');
    }

    return order;
  }
  async confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    if (!orderId) {
      throw VitolaException.ofValidation('ORDERID_INVALID', 'Identificação do pedido inválida');
    }

    if (!orderConfirmed) {
      throw VitolaException.ofValidation('ORDER_INVALID', 'Pedido inválida');
    }

    OrderConfirmedDto.validate(orderConfirmed);

    return await this.orderRepository.confirmOrder(orderId, orderConfirmed);
  }

  async findOrders(): Promise<Array<OrderResponseDto>> {
    const orders = await this.orderRepository.findOrders();
    if (!orders?.length) {
      return [];
    }

    return orders.map((order) => OrderResponseDto.of(order));
  }

  async changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean> {
    if (!orderId) {
      throw VitolaException.ofValidation('ORDERID_IS_REQUIRED', 'Id do pedido inválido');
    }

    if (!status) {
      throw VitolaException.ofValidation('STATUS_IS_REQUIRED', 'Status do pedido inválido');
    }

    return await this.orderRepository.changeOrderStatus(orderId, status);
  }
}
