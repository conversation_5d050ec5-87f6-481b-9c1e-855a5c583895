import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderResponseDto } from '../dto/order-status.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderStatus } from '../models/order-status';

export interface OrderManagementServicePort {
  issueOrder(order: OrderDto): Promise<OrderDto>;
  confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean>;
  findOrders(): Promise<Array<OrderResponseDto>>;
  changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean>;
}
