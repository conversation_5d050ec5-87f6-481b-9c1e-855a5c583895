import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { PaymentResponseDto } from '../dto/payment-response.dto';
import { ApiReturn } from '../../shared/api/models/api-return.model';
import { OrderDto } from '../../../modules/orders/dto/order.dto';

@Injectable()
export class PaymentProxy {
  private readonly paymentApiUrl: string | undefined;

  constructor() {
    this.paymentApiUrl = process.env.PAYMENT_API_URL;
  }

  async issuePayment(paymentRequest: OrderDto): Promise<PaymentResponseDto> {
    try {
      const responseAxios = await axios.post<ApiReturn<PaymentResponseDto>>(`${this.paymentApiUrl}/payments/issue`, paymentRequest);

      const response = responseAxios.data;
      if (!response.success) {
        throw new Error(`Payment API error: ${response.error}`);
      }

      return response.return;
    } catch (error) {
      throw new Error(`Payment API error: ${error.message}`);
    }
  }
}
