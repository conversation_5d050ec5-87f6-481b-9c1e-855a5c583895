import { Injectable } from '@nestjs/common';
import { OrderDto } from '../../../modules/orders/dto/order.dto';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { PaymentResponseDto } from '../dto/payment-response.dto';
import { PaymentProxy } from './payment.proxy';

@Injectable()
export class PaymentService {
  constructor(private readonly paymentProxy: PaymentProxy) {}

  async issuePayment(paymentRequest: OrderDto): Promise<PaymentResponseDto> {
    if (!paymentRequest) {
      throw VitolaException.ofValidation('PAYMENT_DATA_INVALID', 'Dados inválidos para criação do pagamento.');
    }

    return await this.paymentProxy.issuePayment(paymentRequest);
  }
}
