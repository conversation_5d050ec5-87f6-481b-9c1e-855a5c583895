import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { rootMongooseTestModule } from '../mongo-in-memory/mongo-in-memory.config';

const logger = new Logger('MongooseConnection');

type MongooseConnectionType = ReturnType<typeof MongooseModule.forRootAsync>;

let MongooseConnection: MongooseConnectionType;

if (process.env.NODE_ENV === 'test') {
  logger.log('Usando conexao de teste');
  MongooseConnection = rootMongooseTestModule();
} else {
  MongooseConnection = MongooseModule.forRootAsync({
    imports: [ConfigModule],
    inject: [ConfigService],
    useFactory: async (config: ConfigService) => {
      const mongoUri = config.get('MONGO_URI');
      if (!mongoUri) {
        logger.error('A variável MONGO_URI não está definida');
        throw new Error('MONGO_URI is required but not defined.');
      }

      logger.log(`Conectado ao ambiente`);

      return {
        uri: mongoUri,
        useNewUrlParser: true,
        useUnifiedTopology: true,
      };
    },
  });
}

export default MongooseConnection;
