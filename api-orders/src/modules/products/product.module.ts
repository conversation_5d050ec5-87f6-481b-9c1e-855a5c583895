import { MongooseModule } from "@nestjs/mongoose";
import MongooseConnection from "../shared/database/connection/mongoose.connection";
import { Product, ProductSchema } from "./schema/product.schema";
import { ProductManagementServicePortToken, ProductRepositoryPortToken } from "./constants/product.constants";
import { ProductRepository } from "./repositories/product.repository";
import { Module } from "@nestjs/common";
import { ProductsController } from "./controllers/product.controller";
import { ProductManagementService } from "./services/product-management.service";

const MODELS = MongooseModule.forFeature([{ name: Product.name, schema: ProductSchema }]);

@Module({
  imports: [MODELS, MongooseConnection],
  providers: [
    { provide: ProductRepositoryPortToken, useClass: ProductRepository },
    { provide: ProductManagementServicePortToken, useClass: ProductManagementService },
  ],
  controllers: [ProductsController],
})
export class ProductModule {}
