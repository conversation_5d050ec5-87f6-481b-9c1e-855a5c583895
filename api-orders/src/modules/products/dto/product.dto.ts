import { ProductCategory } from '../product-category';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { ApiProperty } from '@nestjs/swagger';

export class ProductDto {
  @ApiProperty({
    description: 'Nome do produto',
    example: 'Misto quente monstrão',
    required: true,
  })
  name: string;

  @ApiProperty({
    description: 'Categoria do produto',
    example: ProductCategory.LANCHE,
    required: true,
  })
  category: ProductCategory;

  @ApiProperty({
    description: 'Descrição do produto',
    example: 'Acompanha tudo do bom e do melhor',
    required: true,
  })
  description: string;

  @ApiProperty({
    description: 'Valor do produto',
    example: 10,
    required: true,
  })
  price: number;

  static validate(product: ProductDto) {
    if (!product.name) {
      throw VitolaException.ofValidation('NAME_IS_REQUIRED', 'Nome do produto é obrigatório.');
    }

    if (!product.category) {
      throw VitolaException.ofValidation('CATEGORY_IS_REQUIRED', 'Categoria do produto é obrigatório.');
    }

    if (!product.description) {
      throw VitolaException.ofValidation('DESCRIPTION_IS_REQUIRED', 'Descrição do produto é obrigatório.');
    }

    if (!product.price) {
      throw VitolaException.ofValidation('PRICE_IS_REQUIRED', 'Preço do produto é obrigatório.');
    }
  }
}
