import { Body, Controller, Get, Inject, Param, Post } from '@nestjs/common';
import { ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ProductManagementServicePortToken, ProductMockDocumentation } from '../constants/product.constants';
import { ProductDto } from '../dto/product.dto';
import { ProductCategory } from '../product-category';
import { Product } from '../schema/product.schema';
import { ProductManagementServicePort } from '../services/product-management.service.port';

@ApiTags('Produtos')
@Controller('products')
export class ProductsController {
  constructor(@Inject(ProductManagementServicePortToken) private readonly productService: ProductManagementServicePort) {}

  @Post()
  @ApiResponse({ status: 200, description: 'Cria produto', schema: { example: { success: true } } })
  @ApiResponse({
    status: 500,
    description: 'Lista de erros',
    content: {
      'aaplication/json': {
        examples: {
          nome_invalido: {
            summary: 'Nome do produto não informado',
            value: { success: false, return: { errorCode: 'NAME_IS_REQUIRED', message: 'Nome do produto é obrigatório' } },
          },
          categoria_invalida: {
            summary: 'Categoria do produto não informada',
            value: { success: false, return: { errorCode: 'CATEGORY_IS_REQUIRED', message: 'Categoria do produto é obrigatório.' } },
          },
          descricao_invalida: {
            summary: 'Descrição do produto não informada',
            value: { success: false, return: { errorCode: 'DESCRIPTION_IS_REQUIRED', message: 'Descrição do produto é obrigatório.' } },
          },
          valor_invalido: {
            summary: 'Valor do produto não informado',
            value: { success: false, return: { errorCode: 'PRICE_IS_REQUIRED', message: 'Preço do produto é obrigatório.' } },
          },
        },
      },
    },
  })
  async create(@Body() product: ProductDto): Promise<void> {
    await this.productService.create(product);
  }

  @Get('categories/:category')
  @ApiParam({
    name: 'category',
    description: 'Categoria do pedido',
    enum: ProductCategory,
    example: ProductCategory.LANCHE,
  })
  @ApiResponse({ status: 200, description: 'Lista de produtos por categorias', schema: { example: { success: true, return: [ProductMockDocumentation] } } })
  @ApiResponse({
    status: 404,
    description: 'Nenhum produto listado',
    schema: {
      example: {
        success: false,
        return: [],
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Categoria não informada',
    schema: {
      example: { success: false, return: { errorCode: 'CATEGORY_IS_REQUIRED', message: 'Categoria de produto é obrigatória' } },
    },
  })
  async findProductsByCategory(@Param('category') category: ProductCategory): Promise<Product[]> {
    return await this.productService.findProductsByCategory(category);
  }
}
