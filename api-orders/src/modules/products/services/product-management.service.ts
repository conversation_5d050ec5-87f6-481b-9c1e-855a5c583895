import { Inject, Injectable } from '@nestjs/common';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { ProductRepositoryPortToken } from '../constants/product.constants';
import { ProductDto } from '../dto/product.dto';
import { ProductCategory } from '../product-category';
import { ProductRepositoryPort } from '../repositories/product.repository.port';
import { Product } from '../schema/product.schema';
import { ProductManagementServicePort } from './product-management.service.port';
@Injectable()
export class ProductManagementService implements ProductManagementServicePort {
  constructor(@Inject(ProductRepositoryPortToken) private readonly productRepository: ProductRepositoryPort) {}

  async findProductsByCategory(category: ProductCategory): Promise<Product[]> {
    if (!category) {
      throw VitolaException.ofValidation('CATEGORY_IS_REQUIRED', 'Categoria de produto é obrigatória.');
    }

    return await this.productRepository.findProductsByCategory(category);
  }

  async create(product: ProductDto): Promise<void> {
    if (!product) {
      throw VitolaException.ofValidation('PRODUCT_IS_NULL', 'Produto não informado, tente novamente');
    }

    ProductDto.validate(product);

    await this.productRepository.create(product);
  }
}
