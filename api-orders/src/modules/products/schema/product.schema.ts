import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';
import { ProductCategory } from '../product-category';

@Schema({
  collection: 'Products',
  versionKey: false,
  _id: true,
})
export class Product extends Document {
  @Prop({ type: Types.ObjectId, default: () => generateNewObjectId(), required: true })
  _id: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
  })
  description: string;

  @Prop({
    type: String,
    enum: [ProductCategory.ACOMPANHAMENTO, ProductCategory.BEBIDA, ProductCategory.LANCHE, ProductCategory.SOBREMESA],
    required: true,
  })
  category: ProductCategory;

  @Prop({
    type: Types.Decimal128,
    required: true,
  })
  price: number;

  @Prop({
    default: () => new Date(),
  })
  createDate: Date;

  @Prop({
    default: () => new Date(),
  })
  updateDate: Date;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
