import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ProductDto } from '../dto/product.dto';
import { Product } from '../schema/product.schema';
import { ProductRepositoryPort } from './product.repository.port';

@Injectable()
export class ProductRepository implements ProductRepositoryPort {
  constructor(@InjectModel('Product') private readonly productModel: Model<Product>) {}
  async findProductsByCategory(category: string): Promise<Product[]> {
    return await this.productModel.find({ category: category });
  }
  
  async create(product: ProductDto): Promise<void> {
    await this.productModel.create(new this.productModel(product));
  }
}
