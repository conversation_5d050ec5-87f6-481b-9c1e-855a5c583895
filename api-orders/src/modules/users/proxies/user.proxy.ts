import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ApiReturn } from '../../shared/api/models/api-return.model';
import { UserInfoDto } from '../dto/user-info.dto';
import { UserDto } from '../dto/user.dto';

@Injectable()
export class UserProxy {
  private readonly costumerApiUrl: string | undefined;

  constructor() {
    this.costumerApiUrl = process.env.COSTUMER_API_URL;
  }

  async save(user: UserDto): Promise<boolean> {
    try {
      const responseAxios = await axios.post<ApiReturn<boolean>>(`${this.costumerApiUrl}/users`, user);

      const response = responseAxios.data;
      if (!response.success) {
        throw new Error(`Costumer API error: ${response.error}`);
      }

      return response.return;
    } catch (error) {
      throw new Error(`Costumer API error: ${error.message}`);
    }
  }

  async findByDocument(document: string): Promise<UserInfoDto | null> {
    try {
      const responseAxios = await axios.get<ApiReturn<UserInfoDto>>(`${this.costumerApiUrl}/users/${document}/info`);

      const response = responseAxios.data;
      if (!response.success) {
        throw new Error(`Costumer API error: ${response.error}`);
      }

      return response.return;
    } catch (error) {
      throw new Error(`Costumer API error: ${error.message}`);
    }
  }
}
