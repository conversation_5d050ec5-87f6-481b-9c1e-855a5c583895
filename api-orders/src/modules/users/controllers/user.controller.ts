import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserInfoDto } from '../dto/user-info.dto';
import { UserDto } from '../dto/user.dto';
import { UserManagementService } from '../services/user-management.service';

@ApiTags('Usuários')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserManagementService) {}

  @Post()
  @ApiResponse({ status: 201, description: 'Usuário criado com sucesso', schema: { example: { success: true } } })
  async create(@Body() user: UserDto): Promise<void> {
    return await this.userService.save(user);
  }

  @Get(':document/infos')
  @ApiParam({
    name: 'document',
    description: 'Documento do usuario',
    example: '12345678900',
  })
  @ApiResponse({
    status: 200,
    description: 'Usuário encontrado com sucesso',
    schema: {
      example: {
        success: true,
        return: {
          document: '12345678900',
          email: '<EMAIL>',
          fullName: 'John Doe',
          id: '678a661f442463bf472d649b',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Usuário não encontrado',
    schema: {
      example: {
        success: false,
        return: null,
        error: 'Usuário não encontrado pelo documento informado',
        errorCode: 'USER_NOT_FOUND',
      },
    },
  })
  async findByDocument(@Param('document') document: string): Promise<UserInfoDto> {
    return await this.userService.findByDocument(document);
  }
}
