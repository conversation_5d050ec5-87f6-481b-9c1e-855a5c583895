import { Injectable, Logger } from '@nestjs/common';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { UserInfoDto } from '../dto/user-info.dto';
import { UserDto } from '../dto/user.dto';
import { UserProxy } from '../proxies/user.proxy';

@Injectable()
export class UserManagementService {
  private readonly logger = new Logger(UserManagementService.name);

  constructor(private readonly userProxy: UserProxy) {}

  async save(user: UserDto): Promise<void> {
    if (!user) {
      throw VitolaException.ofValidation('USER_IS_NULL', 'Dados incorretos, por favor tente novamente');
    }
    UserDto.validate(user);

    try {
      await this.userProxy.save(user);
      this.logger.log(`Usuário criado com sucesso`);
    } catch (exception) {
      this.logger.error(`Erro ao criar usuário: `, exception.message);
      throw exception;
    }
  }

  async findByDocument(document: string): Promise<UserInfoDto> {
    if (!document) {
      throw VitolaException.ofValidation('USER_DOCUMENT_IS_NULL', 'Documento não preenchido para pesquisar, deseja prosseguir sem se identificar?');
    }

    try {
      const userInfoDto = await this.userProxy.findByDocument(document);
      if (!userInfoDto) {
        throw VitolaException.ofNotFound('USER_NOT_FOUND', 'Usuário não encontrado pelo documento informado');
      }

      return userInfoDto;
    } catch (exception) {
      this.logger.error(`Erro ao buscar usuário pelo documento: `, exception.message);
      throw exception;
    }
  }
}
