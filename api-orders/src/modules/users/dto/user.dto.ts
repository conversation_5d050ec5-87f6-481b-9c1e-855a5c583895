import { ApiProperty } from '@nestjs/swagger';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';

export class UserDto {
  @ApiProperty({
    description: 'CPF do usuário',
    example: '12345678900',
    required: true,
  })
  document: string;

  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
    required: true,
  })
  email: string;

  @ApiProperty({
    description: 'Nome do usuário',
    example: '<PERSON> Do<PERSON>',
    required: true,
  })
  fullName: string;

  constructor(document: string, email: string, fullName: string) {
    this.document = document;
    this.email = email;
    this.fullName = fullName;
  }

  static validate(user: UserDto) {
    if (!user?.document?.length) {
      throw VitolaException.ofValidation('USER_DOCUMENT_IS_NULL', 'Por favor preencha o CPF e tente novamente');
    }
  }

  static mockTest() {
    return new UserDto('12345678900', '<EMAIL>', '<PERSON>');
  }

  static mockTestWithoutDocument() {
    return new UserDto('', '<EMAIL>', '<PERSON><PERSON>');
  }
}
