import { ApiProperty } from '@nestjs/swagger';
import { UserDto } from './user.dto';

export class UserInfoDto extends UserDto {
  @ApiProperty({
    description: 'Id do usuário',
    example: '507f1f77bcf86cd799439011',
  })
  id: string;

  constructor(_id: string, document: string, email: string, fullName: string) {
    super(document, email, fullName);
    this.id = _id;
  }

  static mockTest() {
    return new UserInfoDto('678a661f442463bf472d649b', '<PERSON>', '<EMAIL>', '12345678900');
  }
}
