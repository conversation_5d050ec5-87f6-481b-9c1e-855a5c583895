import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { OrderModule } from './modules/orders/order.module';
import { ProductModule } from './modules/products/product.module';
import { SharedApiModule } from './modules/shared/api/modules/shared-api.module';
import MongooseConnection from './modules/shared/database/connection/mongoose.connection';
import { UserModule } from './modules/users/user.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseConnection,
    SharedApiModule,
    OrderModule,
    ProductModule,
    UserModule
  ],
})
export class AppModule {}
