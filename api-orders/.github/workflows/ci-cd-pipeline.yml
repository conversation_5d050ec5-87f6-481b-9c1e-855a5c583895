name: Deploy Api Orders
on:
  push:
    branches:
      - main

jobs:
  CI-CD:
    uses: vitola-lanches/actions/.github/workflows/deploy-nodejs.yml@main
    with:
      service_name: api-orders
      dockerfile_path: Dockerfile
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      CLUSTER_NAME: ${{ secrets.CLUSTER_NAME }}
      PAYMENT_API_URL: ${{ secrets.PAYMENT_API_URL }}
      COSTUMER_API_URL: ${{ secrets.COSTUMER_API_URL }}
      MONGO_URI: ${{ secrets.MONGO_URI }}