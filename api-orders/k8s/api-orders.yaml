apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-orders
  namespace: vitola-lanches
  labels:
    app: api-orders
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-orders
  template:
    metadata:
      labels:
        app: api-orders
        version: v1
    spec:
      containers:
      - name: api-orders
        image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/api-orders:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SONAR_TOKEN
          value: "${SONAR_TOKEN}"
        - name: SONAR_HOST_URL
          value: "${SONAR_HOST_URL}"
        - name: AWS_ACCESS_KEY_ID
          value: "${AWS_ACCESS_KEY_ID}"
        - name: AWS_SECRET_ACCESS_KEY
          value: "${AWS_SECRET_ACCESS_KEY}"
        - name: AWS_REGION
          value: "${AWS_REGION}"
        - name: AWS_ACCOUNT_ID
          value: "${AWS_ACCOUNT_ID}"
        - name: CLUSTER_NAME
          value: "${CLUSTER_NAME}"
        - name: PAYMENT_API_URL
          value: "${PAYMENT_API_URL}"
        - name: COSTUMER_API_URL
          value: "${COSTUMER_API_URL}"
        - name: MONGO_URI
          value: ************************************************************************
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30

---
apiVersion: v1
kind: Service
metadata:
  name: api-orders-service
  namespace: vitola-lanches
  labels:
    app: api-orders
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: api-orders

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-orders-ingress
  namespace: vitola-lanches
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api-orders.vitola-lanches.com
    secretName: api-orders-tls
  rules:
  - host: api-orders.vitola-lanches.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-orders-service
            port:
              number: 8080

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-orders-hpa
  namespace: vitola-lanches
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-orders
  minReplicas: 1
  maxReplicas: 2
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
