import { CallHandler, ExecutionContext } from '@nestjs/common';
import { of, throwError } from 'rxjs';
import { ApiReturnInterceptor } from '../../../../../../src/modules/shared/api/interceptors/api-return.interceptor';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('ApiReturnInterceptor', () => {
  let interceptor: ApiReturnInterceptor;
  let mockExecutionContext: jest.Mocked<ExecutionContext>;
  let mockCallHandler: jest.Mocked<CallHandler>;

  beforeEach(() => {
    interceptor = new ApiReturnInterceptor();

    mockExecutionContext = {
      switchToHttp: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
      getClass: jest.fn(),
      getHandler: jest.fn(),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
    };

    mockCallHandler = {
      handle: jest.fn(),
    };
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('intercept', () => {
    it('should wrap successful response in ApiReturn format', (done) => {
      const testData = { id: 1, name: 'Test' };
      mockCallHandler.handle.mockReturnValue(of(testData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: testData,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle null response data', (done) => {
      mockCallHandler.handle.mockReturnValue(of(null));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: null,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle undefined response data', (done) => {
      mockCallHandler.handle.mockReturnValue(of(undefined));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: undefined,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle empty object response', (done) => {
      const emptyObject = {};
      mockCallHandler.handle.mockReturnValue(of(emptyObject));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: emptyObject,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle array response data', (done) => {
      const arrayData = [{ id: 1 }, { id: 2 }];
      mockCallHandler.handle.mockReturnValue(of(arrayData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: arrayData,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle string response data', (done) => {
      const stringData = 'Test string response';
      mockCallHandler.handle.mockReturnValue(of(stringData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: stringData,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle number response data', (done) => {
      const numberData = 42;
      mockCallHandler.handle.mockReturnValue(of(numberData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: numberData,
          });
          done();
        },
        error: done,
      });
    });

    it('should handle boolean response data', (done) => {
      const booleanData = true;
      mockCallHandler.handle.mockReturnValue(of(booleanData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: booleanData,
          });
          done();
        },
        error: done,
      });
    });

    it('should not interfere with error handling', (done) => {
      const error = new Error('Test error');
      mockCallHandler.handle.mockReturnValue(throwError(() => error));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: () => {
          done(new Error('Should not reach next handler'));
        },
        error: (err) => {
          expect(err).toBe(error);
          done();
        },
      });
    });

    it('should preserve complex nested objects', (done) => {
      const complexData = {
        user: {
          id: 1,
          name: 'John Doe',
          profile: {
            email: '<EMAIL>',
            preferences: {
              theme: 'dark',
              notifications: true,
            },
          },
        },
        orders: [
          { id: 1, total: 100.5 },
          { id: 2, total: 75.25 },
        ],
        metadata: {
          timestamp: new Date(),
          version: '1.0.0',
        },
      };
      mockCallHandler.handle.mockReturnValue(of(complexData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toEqual({
            success: true,
            return: complexData,
          });
          expect(result.return).toEqual(complexData);
          done();
        },
        error: done,
      });
    });

    it('should call handler exactly once', () => {
      const testData = { test: 'data' };
      mockCallHandler.handle.mockReturnValue(of(testData));
      interceptor.intercept(mockExecutionContext, mockCallHandler);
      expect(mockCallHandler.handle).toHaveBeenCalledTimes(1);
    });

    it('should not modify the original data', (done) => {
      const originalData = { id: 1, name: 'Original' };
      const testData = { ...originalData };
      mockCallHandler.handle.mockReturnValue(of(testData));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result.return).toEqual(originalData);
          expect(testData).toEqual(originalData); // Original should be unchanged
          done();
        },
        error: done,
      });
    });
  });

  describe('ApiReturn structure', () => {
    it('should always set success to true for successful responses', (done) => {
      mockCallHandler.handle.mockReturnValue(of('any data'));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result.success).toBe(true);
          done();
        },
        error: done,
      });
    });

    it('should always include return property', (done) => {
      mockCallHandler.handle.mockReturnValue(of('test'));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          expect(result).toHaveProperty('return');
          done();
        },
        error: done,
      });
    });

    it('should only include success and return properties', (done) => {
      mockCallHandler.handle.mockReturnValue(of('test'));
      const result$ = interceptor.intercept(mockExecutionContext, mockCallHandler);
      result$.subscribe({
        next: (result) => {
          const keys = Object.keys(result);
          expect(keys).toHaveLength(2);
          expect(keys).toContain('success');
          expect(keys).toContain('return');
          done();
        },
        error: done,
      });
    });
  });
});
