import { ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Response } from 'express';
import { VitolaException } from '../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { AnyExceptionFilter } from '../../../../../../src/modules/shared/api/filters/exception.filter';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('AnyExceptionFilter', () => {
  let filter: AnyExceptionFilter;
  let mockResponse: jest.Mocked<Response>;
  let mockArgumentsHost: jest.Mocked<ArgumentsHost>;
  let loggerSpy: jest.SpyInstance;

  beforeEach(() => {
    filter = new AnyExceptionFilter();

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    } as any;

    const mockHttpArgumentsHost = {
      getResponse: jest.fn().mockReturnValue(mockResponse),
      getRequest: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue(mockHttpArgumentsHost),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    };

    loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('catch', () => {
    it('should handle HttpException correctly', async () => {
      const message = 'HTTP Exception message';
      const status = HttpStatus.BAD_REQUEST;
      const exception = new HttpException(message, status);
      await filter.catch(exception, mockArgumentsHost);
      expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(status);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          return: null,
          error: message,
        }),
      );
    });

    it('should handle VitolaException correctly', async () => {
      const errorCode = 'VITOLA_ERROR';
      const errorMessage = 'Vitola exception message';
      const exception = VitolaException.ofValidation(errorCode, errorMessage);
      await filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          return: null,
          error: errorMessage,
          errorCode: errorCode,
        }),
      );
    });

    it('should handle generic Error correctly', async () => {
      const message = 'Generic error message';
      const exception = new Error(message);
      await filter.catch(exception as any, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          return: null,
          error: message,
        }),
      );
    });

    it('should log error with stack trace', async () => {
      const message = 'Error for logging';
      const exception = new Error(message);
      await filter.catch(exception as any, mockArgumentsHost);
      expect(loggerSpy).toHaveBeenCalledWith(message, exception.stack);
    });

    it('should handle exception without message', async () => {
      const exception = new Error();
      await filter.catch(exception as any, mockArgumentsHost);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: '',
        }),
      );
    });
  });

  describe('status code handling', () => {
    it('should use 400 for HttpException with BAD_REQUEST', async () => {
      const exception = new HttpException('Bad request', HttpStatus.BAD_REQUEST);
      await filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    });

    it('should use 404 for HttpException with NOT_FOUND', async () => {
      const exception = new HttpException('Not found', HttpStatus.NOT_FOUND);
      await filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NOT_FOUND);
    });

    it('should use 500 for generic Error', async () => {
      const exception = new Error('Generic error');
      await filter.catch(exception as any, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('response structure', () => {
    it('should always return success: false', async () => {
      const exception = new Error('Test');
      await filter.catch(exception as any, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.success).toBe(false);
    });

    it('should always return return: null', async () => {
      const exception = new Error('Test');
      await filter.catch(exception as any, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.return).toBeNull();
    });

    it('should include errorCode for VitolaException', async () => {
      const errorCode = 'TEST_CODE';
      const exception = VitolaException.ofValidation(errorCode, 'Test message');
      await filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.errorCode).toBe(errorCode);
    });
  });

  describe('error message extraction', () => {
    it('should extract message from HttpException', async () => {
      const message = 'HTTP exception message';
      const exception = new HttpException(message, HttpStatus.BAD_REQUEST);
      await filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.error).toBe(message);
    });

    it('should extract message from VitolaException', async () => {
      const message = 'Vitola exception message';
      const exception = VitolaException.ofError('TEST', message);
      await filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.error).toBe(message);
    });

    it('should extract message from generic Error', async () => {
      const message = 'Generic error message';
      const exception = new Error(message);
      await filter.catch(exception as any, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.error).toBe(message);
    });

    it('should extract first message from HttpException response array', async () => {
      const messages = ['First message', 'Second message'];
      const exception = new HttpException({ message: messages }, HttpStatus.BAD_REQUEST);
      await filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.error).toBe(messages[0]);
    });

    it('should use exception message if response message is not an array', async () => {
      const message = 'Exception message';
      const responseMessage = 'Not an array';
      const exception = new HttpException({ message: responseMessage }, HttpStatus.BAD_REQUEST);
      exception.message = message;
      await filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.error).toBe(message);
    });
  });
});
