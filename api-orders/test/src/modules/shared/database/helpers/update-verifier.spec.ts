import { UpdateResult } from 'mongoose';
import { generateNewObjectId } from '../../../../../../src/modules/shared/database/helpers/generate-objectId';
import { UpdateVerifier } from '../../../../../../src/modules/shared/database/helpers/update-verifier';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('UpdateVerifier', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('wasUpdated', () => {
    it('should return true when document was matched and modified', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(true);
    });

    it('should return false when no document was matched', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 0,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(false);
    });

    it('should return false when document was matched but not modified', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(false);
    });

    it('should return true when multiple documents were matched and modified', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 3,
        modifiedCount: 3,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(true);
    });

    it('should return false when multiple documents were matched but none modified', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 3,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(false);
    });

    it('should return true when some of multiple documents were modified', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 3,
        modifiedCount: 2,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(true);
    });

    it('should work regardless of acknowledged status', () => {
      const updateResultAcknowledged: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };

      const updateResultNotAcknowledged: UpdateResult = {
        acknowledged: false,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };
      const resultAcknowledged = UpdateVerifier.wasUpdated(updateResultAcknowledged);
      const resultNotAcknowledged = UpdateVerifier.wasUpdated(updateResultNotAcknowledged);
      expect(resultAcknowledged).toBe(true);
      expect(resultNotAcknowledged).toBe(true);
    });

    it('should work regardless of upserted properties', () => {
      const updateResultWithUpsert: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 1,
        upsertedId: generateNewObjectId(),
      };
      const result = UpdateVerifier.wasUpdated(updateResultWithUpsert);
      expect(result).toBe(true);
    });

    it('should handle edge case with zero counts', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 0,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(false);
    });

    it('should handle edge case with negative counts (theoretical)', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: -1,
        modifiedCount: -1,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(false);
    });

    it('should handle case where matchedCount is greater than modifiedCount', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 5,
        modifiedCount: 3,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(true);
    });

    it('should handle case where modifiedCount equals matchedCount', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 2,
        modifiedCount: 2,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result = UpdateVerifier.wasUpdated(updateResult);
      expect(result).toBe(true);
    });
  });

  describe('static method behavior', () => {
    it('should be a static method', () => {
      // Assert
      expect(typeof UpdateVerifier.wasUpdated).toBe('function');
      expect(UpdateVerifier.wasUpdated).toBe(UpdateVerifier.wasUpdated);
    });

    it('should not require instantiation', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };

      expect(() => UpdateVerifier.wasUpdated(updateResult)).not.toThrow();
    });

    it('should be callable multiple times with same result', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };
      const result1 = UpdateVerifier.wasUpdated(updateResult);
      const result2 = UpdateVerifier.wasUpdated(updateResult);
      const result3 = UpdateVerifier.wasUpdated(updateResult);
      expect(result1).toBe(true);
      expect(result2).toBe(true);
      expect(result3).toBe(true);
      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
    });
  });

  describe('real-world scenarios', () => {
    it('should handle successful single document update', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      };
      const wasUpdated = UpdateVerifier.wasUpdated(updateResult);
      expect(wasUpdated).toBe(true);
    });

    it('should handle document not found scenario', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 0,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const wasUpdated = UpdateVerifier.wasUpdated(updateResult);
      expect(wasUpdated).toBe(false);
    });

    it('should handle no changes needed scenario', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 0,
        upsertedCount: 0,
        upsertedId: null,
      };
      const wasUpdated = UpdateVerifier.wasUpdated(updateResult);
      expect(wasUpdated).toBe(false);
    });

    it('should handle bulk update scenario', () => {
      const updateResult: UpdateResult = {
        acknowledged: true,
        matchedCount: 10,
        modifiedCount: 8,
        upsertedCount: 0,
        upsertedId: null,
      };
      const wasUpdated = UpdateVerifier.wasUpdated(updateResult);
      expect(wasUpdated).toBe(true);
    });
  });
});
