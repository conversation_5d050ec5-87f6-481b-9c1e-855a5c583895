import { Test, TestingModule } from '@nestjs/testing';
import { ProductManagementServicePortToken } from '../../../../../src/modules/products/constants/product.constants';
import { ProductDto } from '../../../../../src/modules/products/dto/product.dto';
import { ProductCategory } from '../../../../../src/modules/products/product-category';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';
import { ProductsController } from '../../../../../src/modules/products/controllers/product.controller';

describe('ProductsController', () => {
  let controller: ProductsController;
  let productService: any;

  const mockProductService = {
    create: jest.fn(),
    findProductsByCategory: jest.fn(),
  };

  const mockProduct = {
    _id: '507f1f77bcf86cd799439011',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Clássico',
    description: 'Delicioso hambúrguer com carne, queijo e salada',
    price: 25.9,
    category: ProductCategory.LANCHE,
    createDate: new Date(),
    updateDate: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductsController],
      providers: [
        {
          provide: ProductManagementServicePortToken,
          useValue: mockProductService,
        },
      ],
    }).compile();

    controller = module.get<ProductsController>(ProductsController);
    productService = module.get(ProductManagementServicePortToken);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new product', async () => {
      const productDto: ProductDto = {
        name: 'Hambúrguer Clássico',
        description: 'Delicioso hambúrguer',
        price: 25.9,
        category: ProductCategory.LANCHE,
      };

      mockProductService.create.mockResolvedValue(undefined);

      await controller.create(productDto);

      expect(productService.create).toHaveBeenCalledWith(productDto);
    });
  });

  describe('findProductsByCategory', () => {
    it('should return products filtered by category', async () => {
      const category = ProductCategory.LANCHE;
      const expectedProducts = [mockProduct];

      mockProductService.findProductsByCategory.mockResolvedValue(expectedProducts);

      const result = await controller.findProductsByCategory(category);

      expect(productService.findProductsByCategory).toHaveBeenCalledWith(category);
      expect(result).toEqual(expectedProducts);
    });
  });
});
