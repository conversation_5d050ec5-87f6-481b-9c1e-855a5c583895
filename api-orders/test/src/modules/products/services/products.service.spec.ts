import { Test, TestingModule } from '@nestjs/testing';
import { ProductRepositoryPortToken } from '../../../../../src/modules/products/constants/product.constants';
import { ProductDto } from '../../../../../src/modules/products/dto/product.dto';
import { ProductCategory } from '../../../../../src/modules/products/product-category';
import { ProductManagementService } from '../../../../../src/modules/products/services/product-management.service';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('ProductManagementService', () => {
  let service: ProductManagementService;
  let productRepository: any;

  const mockProduct = {
    _id: '507f1f77bcf86cd799439011',
    name: 'Hambúrguer Clássico',
    description: 'Delicioso hambúrguer com carne, queijo e salada',
    price: 25.9,
    category: ProductCategory.LANCHE,
    createDate: new Date(),
    updateDate: new Date(),
  };

  const mockProductRepository = {
    findProductsByCategory: jest.fn(),
    create: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductManagementService,
        {
          provide: ProductRepositoryPortToken,
          useValue: mockProductRepository,
        },
      ],
    }).compile();

    service = module.get<ProductManagementService>(ProductManagementService);
    productRepository = module.get(ProductRepositoryPortToken);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('findProductsByCategory', () => {
    it('should return products filtered by category', async () => {
      const category = ProductCategory.LANCHE;
      const products = [mockProduct];

      productRepository.findProductsByCategory.mockResolvedValue(products);

      const result = await service.findProductsByCategory(category);

      expect(productRepository.findProductsByCategory).toHaveBeenCalledWith(category);
      expect(result).toEqual(products);
    });
  });

  describe('create', () => {
    it('should create product successfully', async () => {
      const productDto: ProductDto = {
        name: 'Hambúrguer Clássico',
        description: 'Delicioso hambúrguer',
        price: 25.9,
        category: ProductCategory.LANCHE,
      };

      productRepository.create.mockResolvedValue(undefined);

      await service.create(productDto);

      expect(productRepository.create).toHaveBeenCalledWith(productDto);
    });
  });
});
