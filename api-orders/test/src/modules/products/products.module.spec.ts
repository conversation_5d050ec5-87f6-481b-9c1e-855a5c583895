import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { ProductsController } from '../../../../src/modules/products/controllers/product.controller';
import { ProductModule } from '../../../../src/modules/products/product.module';
import { Product } from '../../../../src/modules/products/schema/product.schema';
import { ProductManagementService } from '../../../../src/modules/products/services/product-management.service';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';
import { createMockModel } from '../../test-utils/mocks/mongoose.mock';

describe('ProductModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [ProductModule],
    })
      .overrideProvider(getModelToken(Product.name))
      .useValue(createMockModel())
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have ProductsController', () => {
    const controller = module.get<ProductsController>(ProductsController);
    expect(controller).toBeDefined();
  });
});
