import { ProductDto } from '../../../../../src/modules/products/dto/product.dto';
import { ProductCategory } from '../../../../../src/modules/products/product-category';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('ProductDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create ProductDto with valid data', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer com carne';
      product.price = 25.9;

      expect(product.name).toBe('Hambúrguer Clássico');
      expect(product.category).toBe(ProductCategory.LANCHE);
      expect(product.description).toBe('Delicioso hambúrguer com carne');
      expect(product.price).toBe(25.9);
    });
  });

  describe('validate', () => {
    it('should not throw when product has valid data', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer';
      product.price = 25.9;

      expect(() => ProductDto.validate(product)).not.toThrow();
    });

    it('should throw VitolaException when name is empty', () => {
      const product = new ProductDto();
      product.name = '';
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer';
      product.price = 25.9;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });

    it('should throw VitolaException when name is null', () => {
      const product = new ProductDto();
      product.name = null as any;
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer';
      product.price = 25.9;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });

    it('should throw VitolaException when category is null', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = null as any;
      product.description = 'Delicioso hambúrguer';
      product.price = 25.9;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });

    it('should throw VitolaException when description is empty', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = ProductCategory.LANCHE;
      product.description = '';
      product.price = 25.9;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });

    it('should throw VitolaException when price is zero', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer';
      product.price = 0;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });

    it('should throw VitolaException when price is null', () => {
      const product = new ProductDto();
      product.name = 'Hambúrguer Clássico';
      product.category = ProductCategory.LANCHE;
      product.description = 'Delicioso hambúrguer';
      product.price = null as any;

      expect(() => ProductDto.validate(product)).toThrow(VitolaException);
    });
  });

  describe('category validation', () => {
    it('should accept LANCHE category', () => {
      const product = new ProductDto();
      product.category = ProductCategory.LANCHE;

      expect(product.category).toBe(ProductCategory.LANCHE);
    });

    it('should accept BEBIDA category', () => {
      const product = new ProductDto();
      product.category = ProductCategory.BEBIDA;

      expect(product.category).toBe(ProductCategory.BEBIDA);
    });

    it('should accept ACOMPANHAMENTO category', () => {
      const product = new ProductDto();
      product.category = ProductCategory.ACOMPANHAMENTO;

      expect(product.category).toBe(ProductCategory.ACOMPANHAMENTO);
    });

    it('should accept SOBREMESA category', () => {
      const product = new ProductDto();
      product.category = ProductCategory.SOBREMESA;

      expect(product.category).toBe(ProductCategory.SOBREMESA);
    });
  });
});
