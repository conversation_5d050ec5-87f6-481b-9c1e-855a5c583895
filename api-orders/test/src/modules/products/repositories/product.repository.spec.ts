import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { ProductDto } from '../../../../../src/modules/products/dto/product.dto';
import { ProductCategory } from '../../../../../src/modules/products/product-category';
import { ProductRepository } from '../../../../../src/modules/products/repositories/product.repository';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('ProductRepository', () => {
  let repository: ProductRepository;
  let productModel: any;

  const mockProduct = {
    _id: '507f1f77bcf86cd799439011',
    name: 'Hambúrguer Clássico',
    description: 'Delicioso hambúrguer com carne, queijo e salada',
    price: 25.9,
    category: ProductCategory.LANCHE,
    createDate: new Date(),
    updateDate: new Date(),
  };

  const mockProductModel = {
    find: jest.fn(),
    create: jest.fn(),
    constructor: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductRepository,
        {
          provide: getModelToken('Product'),
          useValue: mockProductModel,
        },
      ],
    }).compile();

    repository = module.get<ProductRepository>(ProductRepository);
    productModel = module.get(getModelToken('Product'));
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('findProductsByCategory', () => {
    it('should return products filtered by category', async () => {
      const category = ProductCategory.LANCHE;
      const expectedProducts = [mockProduct];

      mockProductModel.find.mockResolvedValue(expectedProducts);

      const result = await repository.findProductsByCategory(category);

      expect(mockProductModel.find).toHaveBeenCalledWith({ category: category });
      expect(result).toEqual(expectedProducts);
    });

    it('should return empty array when no products found', async () => {
      const category = ProductCategory.BEBIDA;

      mockProductModel.find.mockResolvedValue([]);

      const result = await repository.findProductsByCategory(category);

      expect(mockProductModel.find).toHaveBeenCalledWith({ category: category });
      expect(result).toEqual([]);
    });
  });
});
