import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { UserProxy } from '../../../../../src/modules/users/proxies/user.proxy';
import { UserDto } from '../../../../../src/modules/users/dto/user.dto';
import { UserInfoDto } from '../../../../../src/modules/users/dto/user-info.dto';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('UserProxy', () => {
  let service: UserProxy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserProxy],
    }).compile();

    service = module.get<UserProxy>(UserProxy);
    process.env.COSTUMER_API_URL = 'https://api.customer.com';
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('save', () => {
    it('should handle API errors when success is false', async () => {
      const userDto = UserDto.mockTest();
      const mockResponse = {
        data: {
          success: false,
          error: 'User creation failed',
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      await expect(service.save(userDto)).rejects.toThrow('Costumer API error: User creation failed');
    });

    it('should handle network errors', async () => {
      const userDto = UserDto.mockTest();

      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      await expect(service.save(userDto)).rejects.toThrow('Costumer API error: Network error');
    });
  });

  describe('findByDocument', () => {
    it('should find user by document successfully', async () => {
      const document = '12345678900';
      const expectedUser = UserInfoDto.mockTest();
      const mockResponse = {
        data: {
          success: true,
          return: expectedUser,
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await service.findByDocument(document);

      expect(mockedAxios.get).toHaveBeenCalledWith(`https://api.customer.com/users/${document}/info`);
      expect(result).toEqual(expectedUser);
    });

    it('should handle API errors when success is false', async () => {
      const document = '12345678900';
      const mockResponse = {
        data: {
          success: false,
          error: 'User not found',
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      await expect(service.findByDocument(document)).rejects.toThrow('Costumer API error: User not found');
    });

    it('should handle network errors', async () => {
      const document = '12345678900';

      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      await expect(service.findByDocument(document)).rejects.toThrow('Costumer API error: Network error');
    });
  });
});
