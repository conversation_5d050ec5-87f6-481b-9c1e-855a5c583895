import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from '../../../../../src/modules/users/controllers/user.controller';
import { UserManagementService } from '../../../../../src/modules/users/services/user-management.service';
import { UserDto } from '../../../../../src/modules/users/dto/user.dto';
import { UserInfoDto } from '../../../../../src/modules/users/dto/user-info.dto';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('UserController', () => {
  let controller: UserController;
  let userService: UserManagementService;

  const mockUserService = {
    save: jest.fn(),
    findByDocument: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserManagementService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserManagementService>(UserManagementService);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const userDto = UserDto.mockTest();
      
      mockUserService.save.mockResolvedValue(undefined);

      await controller.create(userDto);

      expect(userService.save).toHaveBeenCalledWith(userDto);
    });
  });

  describe('findByDocument', () => {
    it('should return user info by document', async () => {
      const document = '12345678900';
      const expectedUser = UserInfoDto.mockTest();
      
      mockUserService.findByDocument.mockResolvedValue(expectedUser);

      const result = await controller.findByDocument(document);

      expect(userService.findByDocument).toHaveBeenCalledWith(document);
      expect(result).toEqual(expectedUser);
    });
  });
});