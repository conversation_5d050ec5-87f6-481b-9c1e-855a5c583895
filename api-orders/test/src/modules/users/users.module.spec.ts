import { Test, TestingModule } from '@nestjs/testing';
import { UserModule } from '../../../../src/modules/users/user.module';
import { UserController } from '../../../../src/modules/users/controllers/user.controller';
import { UserManagementService } from '../../../../src/modules/users/services/user-management.service';
import { UserProxy } from '../../../../src/modules/users/proxies/user.proxy';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';

describe('UserModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [UserModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have UserController', () => {
    const controller = module.get<UserController>(UserController);
    expect(controller).toBeDefined();
  });

  it('should have UserManagementService', () => {
    const service = module.get<UserManagementService>(UserManagementService);
    expect(service).toBeDefined();
  });

  it('should have UserProxy', () => {
    const proxy = module.get<UserProxy>(UserProxy);
    expect(proxy).toBeDefined();
  });
});