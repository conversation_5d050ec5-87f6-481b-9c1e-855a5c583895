import { Test, TestingModule } from '@nestjs/testing';
import { UserManagementService } from '../../../../../src/modules/users/services/user-management.service';
import { UserProxy } from '../../../../../src/modules/users/proxies/user.proxy';
import { UserDto } from '../../../../../src/modules/users/dto/user.dto';
import { UserInfoDto } from '../../../../../src/modules/users/dto/user-info.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('UserManagementService', () => {
  let service: UserManagementService;
  let userProxy: UserProxy;

  const mockUserProxy = {
    save: jest.fn(),
    findByDocument: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserManagementService,
        {
          provide: UserProxy,
          useValue: mockUserProxy,
        },
      ],
    }).compile();

    service = module.get<UserManagementService>(UserManagementService);
    userProxy = module.get<UserProxy>(UserProxy);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('save', () => {
    it('should save user successfully', async () => {
      const userDto = UserDto.mockTest();

      mockUserProxy.save.mockResolvedValue(true);

      await service.save(userDto);

      expect(userProxy.save).toHaveBeenCalledWith(userDto);
    });

    it('should throw VitolaException when user document is empty', async () => {
      const userDto = UserDto.mockTestWithoutDocument();

      await expect(service.save(userDto)).rejects.toThrow(VitolaException);
    });
  });

  describe('findByDocument', () => {
    it('should return user info by document', async () => {
      const document = '12345678900';
      const expectedUser = UserInfoDto.mockTest();

      mockUserProxy.findByDocument.mockResolvedValue(expectedUser);

      const result = await service.findByDocument(document);

      expect(userProxy.findByDocument).toHaveBeenCalledWith(document);
      expect(result).toEqual(expectedUser);
    });

    it('should throw VitolaException when document is empty', async () => {
      await expect(service.findByDocument('')).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when user not found', async () => {
      const document = '12345678900';

      mockUserProxy.findByDocument.mockResolvedValue(null);

      await expect(service.findByDocument(document)).rejects.toThrow(VitolaException);
    });
  });
});
