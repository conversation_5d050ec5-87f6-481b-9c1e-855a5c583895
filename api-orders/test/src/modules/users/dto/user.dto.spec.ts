import { UserDto } from '../../../../../src/modules/users/dto/user.dto';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('UserDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create UserDto with valid data', () => {
      const user = new UserDto('12345678900', '<EMAIL>', '<PERSON>');

      expect(user.document).toBe('12345678900');
      expect(user.email).toBe('<EMAIL>');
      expect(user.fullName).toBe('<PERSON>');
    });
  });

  describe('validate', () => {
    it('should not throw when user has valid document', () => {
      const user = UserDto.mockTest();

      expect(() => UserDto.validate(user)).not.toThrow();
    });

    it('should throw VitolaException when document is empty', () => {
      const user = UserDto.mockTestWithoutDocument();

      expect(() => UserDto.validate(user)).toThrow();
    });
  });

  describe('mockTest', () => {
    it('should return valid test user', () => {
      const user = UserDto.mockTest();

      expect(user.document).toBe('12345678900');
      expect(user.email).toBe('<EMAIL>');
      expect(user.fullName).toBe('John Doe');
    });
  });

  describe('mockTestWithoutDocument', () => {
    it('should return user without document', () => {
      const user = UserDto.mockTestWithoutDocument();

      expect(user.document).toBe('');
      expect(user.email).toBe('<EMAIL>');
      expect(user.fullName).toBe('John Doe');
    });
  });
});
