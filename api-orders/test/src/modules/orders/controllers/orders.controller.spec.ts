import { Test, TestingModule } from '@nestjs/testing';
import { OrderManagementServicePortToken } from '../../../../../src/modules/orders/constants/order.constants';
import { OrderController } from '../../../../../src/modules/orders/controllers/order.controller';
import { OrderResponseDto } from '../../../../../src/modules/orders/dto/order-status.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderController', () => {
  let controller: OrderController;
  let orderService: any;

  const mockOrderService = {
    issueOrder: jest.fn(),
    findOrders: jest.fn(),
    confirmOrder: jest.fn(),
    changeOrderStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderController],
      providers: [
        {
          provide: OrderManagementServicePortToken,
          useValue: mockOrderService,
        },
      ],
    }).compile();

    controller = module.get<OrderController>(OrderController);
    orderService = module.get(OrderManagementServicePortToken);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('issueOrder', () => {
    it('should issue a new order', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const expectedOrder = OrderFactory.createOrderDto();

      mockOrderService.issueOrder.mockResolvedValue(expectedOrder);

      const result = await controller.issueOrder(orderDto);

      expect(orderService.issueOrder).toHaveBeenCalledWith(orderDto);
      expect(result).toEqual(expectedOrder);
    });
  });

  describe('findOrdersStatus', () => {
    it('should return an array of order status', async () => {
      const expectedOrders: OrderResponseDto[] = [
        {
          id: '507f1f77bcf86cd799439013',
          orderNumber: 123,
          orderStatus: OrderStatus.RECEIVED,
          receivedDate: new Date(),
          lastUpdateDate: new Date(),
        },
      ];

      mockOrderService.findOrders.mockResolvedValue(expectedOrders);

      const result = await controller.findOrdersStatus();

      expect(orderService.findOrders).toHaveBeenCalled();
      expect(result).toEqual(expectedOrders);
    });
  });

  describe('confirmeOrder', () => {
    it('should confirm an order', async () => {
      const orderId = '507f1f77bcf86cd799439013';
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      mockOrderService.confirmOrder.mockResolvedValue(true);

      const result = await controller.confirmeOrder(orderId, orderConfirmed);

      expect(orderService.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmed);
      expect(result).toBe(true);
    });
  });

  describe('changeOrderStatus', () => {
    it('should change order status', async () => {
      const orderId = '507f1f77bcf86cd799439013';
      const status = OrderStatus.PREPARING;

      mockOrderService.changeOrderStatus.mockResolvedValue(true);

      const result = await controller.changeOrderStatus(orderId, status);

      expect(orderService.changeOrderStatus).toHaveBeenCalledWith(orderId, status);
      expect(result).toBe(true);
    });
  });
});
