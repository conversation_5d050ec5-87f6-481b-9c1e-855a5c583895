import { Test, TestingModule } from '@nestjs/testing';
import { OrderRepositoryPortToken } from '../../../../../src/modules/orders/constants/order.constants';
import { OrderConfirmedDto } from '../../../../../src/modules/orders/dto/order-confirmed.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { OrderManagementService } from '../../../../../src/modules/orders/services/order-management.service';
import { PaymentService } from '../../../../../src/modules/payments/services/payment.service';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderManagementService', () => {
  let service: OrderManagementService;
  let orderRepository: any;
  let paymentService: PaymentService;

  const mockOrderRepository = {
    create: jest.fn(),
    findOrders: jest.fn(),
    confirmOrder: jest.fn(),
    changeOrderStatus: jest.fn(),
  };

  const mockPaymentService = {
    issuePayment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderManagementService,
        {
          provide: OrderRepositoryPortToken,
          useValue: mockOrderRepository,
        },
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
      ],
    }).compile();

    service = module.get<OrderManagementService>(OrderManagementService);
    orderRepository = module.get(OrderRepositoryPortToken);
    paymentService = module.get<PaymentService>(PaymentService);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('issueOrder', () => {
    it('should issue order successfully', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const paymentInfo = { paymentUrl: 'test-url', qrCode: 'test-qr' };

      mockPaymentService.issuePayment.mockResolvedValue(paymentInfo);
      mockOrderRepository.create.mockResolvedValue('order-id-123');

      const result = await service.issueOrder(orderDto);

      expect(mockPaymentService.issuePayment).toHaveBeenCalled();
      expect(mockOrderRepository.create).toHaveBeenCalledWith(orderDto);
      expect(result.id).toBe('order-id-123');
    });

    it('should throw VitolaException when order is null', async () => {
      await expect(service.issueOrder(null as any)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when order is undefined', async () => {
      await expect(service.issueOrder(undefined as any)).rejects.toThrow(VitolaException);
    });

    it('should log error and throw VitolaException when order creation fails', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const paymentInfo = { paymentUrl: 'test-url', qrCode: 'test-qr' };
      const loggerSpy = jest.spyOn(service['logger'], 'log');

      mockPaymentService.issuePayment.mockResolvedValue(paymentInfo);
      mockOrderRepository.create.mockRejectedValue(new Error('Database error'));

      await expect(service.issueOrder(orderDto)).rejects.toThrow(VitolaException);
      expect(loggerSpy).toHaveBeenCalledWith(expect.stringContaining('Erro durante criação do pedido:'));
    });
  });

  describe('confirmOrder', () => {
    it('should confirm order successfully', async () => {
      const orderId = '507f1f77bcf86cd799439013';
      const orderConfirmed = new OrderConfirmedDto();
      orderConfirmed.totalPaid = 10;

      mockOrderRepository.confirmOrder.mockResolvedValue(true);

      const result = await service.confirmOrder(orderId, orderConfirmed);

      expect(mockOrderRepository.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmed);
      expect(result).toBe(true);
    });

    it('should throw VitolaException when orderId is invalid', async () => {
      const orderConfirmed = new OrderConfirmedDto();

      await expect(service.confirmOrder('', orderConfirmed)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when orderConfirmed is null', async () => {
      const orderId = '507f1f77bcf86cd799439013';

      await expect(service.confirmOrder(orderId, null as any)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when orderConfirmed is undefined', async () => {
      const orderId = '507f1f77bcf86cd799439013';

      await expect(service.confirmOrder(orderId, undefined as any)).rejects.toThrow(VitolaException);
    });
  });

  describe('findOrders', () => {
    it('should return empty array when no orders found', async () => {
      mockOrderRepository.findOrders.mockResolvedValue([]);

      const result = await service.findOrders();

      expect(mockOrderRepository.findOrders).toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('changeOrderStatus', () => {
    it('should change order status successfully', async () => {
      const orderId = '507f1f77bcf86cd799439013';
      const status = OrderStatus.PREPARING;

      mockOrderRepository.changeOrderStatus.mockResolvedValue(true);

      const result = await service.changeOrderStatus(orderId, status);

      expect(mockOrderRepository.changeOrderStatus).toHaveBeenCalledWith(orderId, status);
      expect(result).toBe(true);
    });

    it('should throw VitolaException when orderId is invalid', async () => {
      const status = OrderStatus.PREPARING;

      await expect(service.changeOrderStatus('', status)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when status is null', async () => {
      const orderId = '507f1f77bcf86cd799439013';

      await expect(service.changeOrderStatus(orderId, null as any)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when status is undefined', async () => {
      const orderId = '507f1f77bcf86cd799439013';

      await expect(service.changeOrderStatus(orderId, undefined as any)).rejects.toThrow(VitolaException);
    });
  });
});
