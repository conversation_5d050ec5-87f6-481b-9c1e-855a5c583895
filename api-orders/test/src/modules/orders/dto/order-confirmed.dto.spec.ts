import { OrderConfirmedDto } from '../../../../../src/modules/orders/dto/order-confirmed.dto';
import { generateNewObjectId } from '../../../../../src/modules/shared/database/helpers/generate-objectId';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderConfirmedDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create OrderConfirmedDto with valid data', () => {
      const orderId = generateNewObjectId().toString();
      const dto = new OrderConfirmedDto();
      dto.orderId = orderId;
      dto.paymentProof = 'proof-123456';
      dto.confirmed = true;
      dto.totalPaid = 25.0;

      expect(dto.orderId).toBe(orderId);
      expect(dto.paymentProof).toBe('proof-123456');
      expect(dto.confirmed).toBe(true);
      expect(dto.totalPaid).toBe(25.0);
    });

    it('should create OrderConfirmedDto with confirmed false', () => {
      const dto = new OrderConfirmedDto();
      dto.confirmed = false;
      dto.totalPaid = 0;

      expect(dto.confirmed).toBe(false);
      expect(dto.totalPaid).toBe(0);
    });
  });

  describe('properties validation', () => {
    it('should accept valid orderId', () => {
      const dto = new OrderConfirmedDto();
      const validId = generateNewObjectId().toString();

      dto.orderId = validId;

      expect(dto.orderId).toBe(validId);
    });

    it('should accept positive totalPaid', () => {
      const dto = new OrderConfirmedDto();

      dto.totalPaid = 50.75;

      expect(dto.totalPaid).toBe(50.75);
    });

    it('should accept payment proof string', () => {
      const dto = new OrderConfirmedDto();
      const proof = 'payment-proof-abc123';

      dto.paymentProof = proof;

      expect(dto.paymentProof).toBe(proof);
    });
  });
});
