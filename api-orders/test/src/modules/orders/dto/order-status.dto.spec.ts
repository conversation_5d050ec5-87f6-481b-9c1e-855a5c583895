import { OrderResponseDto } from '../../../../../src/modules/orders/dto/order-status.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { generateNewObjectId } from '../../../../../src/modules/shared/database/helpers/generate-objectId';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderResponseDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create OrderResponseDto with valid data', () => {
      const id = generateNewObjectId().toString();
      const receivedDate = new Date();
      const lastUpdateDate = new Date();

      const dto = new OrderResponseDto(id, 123456, OrderStatus.RECEIVED, receivedDate, lastUpdateDate);
      expect(dto.id).toBe(id);
      expect(dto.orderNumber).toBe(123456);
      expect(dto.orderStatus).toBe(OrderStatus.RECEIVED);
      expect(dto.receivedDate).toBe(receivedDate);
      expect(dto.lastUpdateDate).toBe(lastUpdateDate);
    });
  });

  describe('order status values', () => {
    it('should accept RECEIVED status', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());

      dto.orderStatus = OrderStatus.RECEIVED;

      expect(dto.orderStatus).toBe(OrderStatus.RECEIVED);
    });

    it('should accept PREPARING status', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());

      dto.orderStatus = OrderStatus.PREPARING;

      expect(dto.orderStatus).toBe(OrderStatus.PREPARING);
    });

    it('should accept COMPLETED status', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());

      dto.orderStatus = OrderStatus.COMPLETED;

      expect(dto.orderStatus).toBe(OrderStatus.COMPLETED);
    });
  });

  describe('date handling', () => {
    it('should handle date objects correctly', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());
      const testDate = new Date('2024-01-15T10:30:00Z');

      dto.receivedDate = testDate;
      dto.lastUpdateDate = testDate;

      expect(dto.receivedDate).toEqual(testDate);
      expect(dto.lastUpdateDate).toEqual(testDate);
    });

    it('should handle different dates for received and update', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());
      const receivedDate = new Date('2024-01-15T10:30:00Z');
      const updateDate = new Date('2024-01-15T11:30:00Z');

      dto.receivedDate = receivedDate;
      dto.lastUpdateDate = updateDate;

      expect(dto.receivedDate).toEqual(receivedDate);
      expect(dto.lastUpdateDate).toEqual(updateDate);
      expect(dto.lastUpdateDate.getTime()).toBeGreaterThan(dto.receivedDate.getTime());
    });
  });

  describe('order number validation', () => {
    it('should accept positive order numbers', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());

      dto.orderNumber = 999999;

      expect(dto.orderNumber).toBe(999999);
    });

    it('should accept single digit order numbers', () => {
      const dto = new OrderResponseDto(generateNewObjectId().toString(), 123456, OrderStatus.RECEIVED, new Date(), new Date());

      dto.orderNumber = 1;

      expect(dto.orderNumber).toBe(1);
    });
  });
});
