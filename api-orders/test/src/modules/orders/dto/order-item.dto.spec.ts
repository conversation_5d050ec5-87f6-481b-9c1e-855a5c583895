import { OrderItemDto } from '../../../../../src/modules/orders/dto/order-item.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderItemDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create OrderItemDto with valid data', () => {
      const item = new OrderItemDto();
      item.name = 'Misto Quente';
      item.description = 'Delicioso misto quente';
      item.quantity = 2;
      item.unitPrice = 15.5;

      expect(item.name).toBe('Misto Quente');
      expect(item.description).toBe('Delicioso misto quente');
      expect(item.quantity).toBe(2);
      expect(item.unitPrice).toBe(15.5);
    });
  });

  describe('validate', () => {
    it('should not throw when item has valid data', () => {
      const item = new OrderItemDto();
      item.name = 'Misto Quente';
      item.description = 'Delicioso misto quente';
      item.quantity = 1;
      item.unitPrice = 15.5;

      expect(() => OrderItemDto.validate(item)).not.toThrow();
    });

    it('should throw VitolaException when name is empty', () => {
      const item = new OrderItemDto();
      item.name = '';
      item.description = 'Delicioso misto quente';
      item.quantity = 1;
      item.unitPrice = 15.5;

      expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
    });

    it('should throw VitolaException when quantity is zero', () => {
      const item = new OrderItemDto();
      item.name = 'Misto Quente';
      item.description = 'Delicioso misto quente';
      item.quantity = 0;
      item.unitPrice = 15.5;

      expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
    });

    it('should throw VitolaException when unitPrice is negative', () => {
      const item = new OrderItemDto();
      item.name = 'Misto Quente';
      item.description = 'Delicioso misto quente';
      item.quantity = 1;
      item.unitPrice = -5;

      expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
    });
  });
});
