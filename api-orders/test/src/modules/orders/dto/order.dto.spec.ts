import { OrderDto } from '../../../../../src/modules/orders/dto/order.dto';
import { PaymentMethod } from '../../../../../src/modules/orders/models/order-payment-method';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create OrderDto with valid data', () => {
      const order = new OrderDto();
      order.document = '12345678900';
      order.total = 15.5;
      order.items = [OrderFactory.createOrderItemDto()];

      expect(order.document).toBe('12345678900');
      expect(order.items).toHaveLength(1);
      expect(order.total).toBe(15.5);
    });
  });

  describe('validate', () => {
    it('should validate order successfully', () => {
      const order = OrderFactory.createOrderDto();

      expect(() => OrderDto.validate(order)).not.toThrow();
      expect(order.id).toBeDefined();
      expect(order.status).toBe(OrderStatus.RECEIVED);
      expect(order.orderNumber).toBeDefined();
    });

    it('should throw VitolaException when items are empty', () => {
      const order = new OrderDto();
      order.document = '12345678900';
      order.total = 0;

      expect(() => OrderDto.validate(order)).toThrow(VitolaException);
    });

    it('should throw VitolaException when items are null', () => {
      const order = new OrderDto();
      order.document = '12345678900';
      order.total = 0;

      expect(() => OrderDto.validate(order)).toThrow(VitolaException);
    });

    it('should calculate total correctly', () => {
      const order = OrderFactory.createOrderWithMultipleItems(2);

      OrderDto.validate(order);

      expect(order.total).toBeGreaterThan(0);
    });
  });

  describe('validatePayment', () => {
    it('should validate payment successfully', () => {
      const order = OrderFactory.createOrderDto();
      order.paymentInformation = {
        paymentMethod: PaymentMethod.QRCODEPIX,
        paymentUrl: 'test-url',
        qrCodeUrl: 'test-qr',
        paymentProof: 'proof',
        totalPaid: 15.5,
        confirmed: true,
        createDate: new Date(),
        updateDate: new Date(),
      };

      expect(() => OrderDto.validatePayment(order)).not.toThrow();
    });

    it('should throw VitolaException when payment information is missing', () => {
      const order = OrderFactory.createOrderDto();

      expect(() => OrderDto.validatePayment(order)).toThrow(VitolaException);
    });
  });

  describe('calculateTotal', () => {
    it('should calculate total for single item', () => {
      const order = OrderFactory.createOrderDto();

      const total = OrderDto.calculateTotal(order);

      expect(total).toBe(15.5);
    });

    it('should calculate total for multiple items', () => {
      const order = OrderFactory.createOrderWithMultipleItems(3);

      const total = OrderDto.calculateTotal(order);

      expect(total).toBeGreaterThan(0);
    });
  });
});
