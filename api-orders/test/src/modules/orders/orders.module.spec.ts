import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { OrderController } from '../../../../src/modules/orders/controllers/order.controller';
import { OrderModule } from '../../../../src/modules/orders/order.module';
import { Order } from '../../../../src/modules/orders/schema/order.schema';
import { PaymentProxy } from '../../../../src/modules/payments/services/payment.proxy';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';
import { createMockModel } from '../../test-utils/mocks/mongoose.mock';

describe('OrderModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [OrderModule],
    })
      .overrideProvider(getModelToken(Order.name))
      .useValue(createMockModel())
      .overrideProvider(PaymentProxy)
      .useValue({ issuePayment: jest.fn() })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have OrderController', () => {
    const controller = module.get<OrderController>(OrderController);
    expect(controller).toBeDefined();
  });
});
