import { PaymentResponseDto } from '../../../../../src/modules/payments/dto/payment-response.dto';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('PaymentResponseDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create PaymentResponseDto with valid data', () => {
      const paymentUrl = 'https://payment-url.com';
      const qrCode = 'https://qr-code.com';

      const response = new PaymentResponseDto(paymentUrl, qrCode);

      expect(response.paymentUrl).toBe(paymentUrl);
      expect(response.qrCode).toBe(qrCode);
    });

    it('should create PaymentResponseDto with empty strings', () => {
      const response = new PaymentResponseDto('', '');

      expect(response.paymentUrl).toBe('');
      expect(response.qrCode).toBe('');
    });
  });

  describe('of', () => {
    it('should create PaymentResponseDto with provided values', () => {
      const paymentUrl = 'https://payment-url.com';
      const qrCode = 'https://qr-code.com';

      const response = PaymentResponseDto.of(paymentUrl, qrCode);

      expect(response).toBeInstanceOf(PaymentResponseDto);
      expect(response.paymentUrl).toBe(paymentUrl);
      expect(response.qrCode).toBe(qrCode);
    });

    it('should create PaymentResponseDto with default empty strings when no parameters', () => {
      const response = PaymentResponseDto.of();

      expect(response).toBeInstanceOf(PaymentResponseDto);
      expect(response.paymentUrl).toBe('');
      expect(response.qrCode).toBe('');
    });

    it('should create PaymentResponseDto with default empty strings when undefined parameters', () => {
      const response = PaymentResponseDto.of(undefined, undefined);

      expect(response).toBeInstanceOf(PaymentResponseDto);
      expect(response.paymentUrl).toBe('');
      expect(response.qrCode).toBe('');
    });

    it('should handle mixed undefined and valid parameters', () => {
      const paymentUrl = 'https://payment-url.com';

      const response1 = PaymentResponseDto.of(paymentUrl, undefined);
      expect(response1.paymentUrl).toBe(paymentUrl);
      expect(response1.qrCode).toBe('');

      const qrCode = 'https://qr-code.com';
      const response2 = PaymentResponseDto.of(undefined, qrCode);
      expect(response2.paymentUrl).toBe('');
      expect(response2.qrCode).toBe(qrCode);
    });
  });
});
