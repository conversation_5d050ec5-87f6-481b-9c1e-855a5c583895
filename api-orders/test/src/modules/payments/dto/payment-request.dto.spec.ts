import { PaymentRequestDto } from '../../../../../src/modules/payments/dto/payment-request.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('PaymentRequestDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create PaymentRequestDto with valid data', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 100, 2);

      expect(paymentRequest.id).toBe('order-123');
      expect(paymentRequest.title).toBe('Test Payment');
      expect(paymentRequest.unit_price).toBe(100);
      expect(paymentRequest.quantity).toBe(2);
    });

    it('should create PaymentRequestDto with minimum valid values', () => {
      const paymentRequest = new PaymentRequestDto('1', 'Item', 0.01, 1);

      expect(paymentRequest.id).toBe('1');
      expect(paymentRequest.title).toBe('Item');
      expect(paymentRequest.unit_price).toBe(0.01);
      expect(paymentRequest.quantity).toBe(1);
    });
  });

  describe('validate', () => {
    it('should not throw when payment request has valid data', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 100, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).not.toThrow();
    });

    it('should throw VitolaException when title is empty', () => {
      const paymentRequest = new PaymentRequestDto('order-123', '', 100, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when title is null', () => {
      const paymentRequest = new PaymentRequestDto('order-123', null as any, 100, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when title is undefined', () => {
      const paymentRequest = new PaymentRequestDto('order-123', undefined as any, 100, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when unit_price is zero', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 0, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when unit_price is negative', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', -10, 2);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when quantity is zero', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 100, 0);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when quantity is negative', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 100, -1);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });

    it('should throw VitolaException when multiple fields are invalid', () => {
      const paymentRequest = new PaymentRequestDto('order-123', '', -10, 0);

      expect(() => PaymentRequestDto.validate(paymentRequest)).toThrow(VitolaException);
    });
  });

  describe('edge cases', () => {
    it('should handle very small positive unit_price', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 0.01, 1);

      expect(() => PaymentRequestDto.validate(paymentRequest)).not.toThrow();
    });

    it('should handle large values', () => {
      const paymentRequest = new PaymentRequestDto('order-123', 'Test Payment', 999999.99, 100);

      expect(() => PaymentRequestDto.validate(paymentRequest)).not.toThrow();
    });
  });
});
