import { Test, TestingModule } from '@nestjs/testing';
import { PaymentModule } from '../../../../src/modules/payments/payment.module';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';
import { PaymentProxy } from '../../../../src/modules/payments/services/payment.proxy';
import { PaymentService } from '../../../../src/modules/payments/services/payment.service';

describe('PaymentModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PaymentModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have PaymentService', () => {
    const service = module.get<PaymentService>(PaymentService);
    expect(service).toBeDefined();
  });

  it('should have PaymentProxy', () => {
    const proxy = module.get<PaymentProxy>(PaymentProxy);
    expect(proxy).toBeDefined();
  });

  it('should export PaymentService', () => {
    const service = module.get<PaymentService>(PaymentService);
    expect(service).toBeDefined();
  });
});
