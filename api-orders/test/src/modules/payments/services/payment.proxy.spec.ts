import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { PaymentRequestDto } from '../../../../../src/modules/payments/dto/payment-request.dto';
import { PaymentProxy } from '../../../../../src/modules/payments/services/payment.proxy';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';
import { OrderDto } from '../../../../../src/modules/orders/dto/order.dto';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('PaymentProxy', () => {
  let service: PaymentProxy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PaymentProxy],
    }).compile();

    service = module.get<PaymentProxy>(PaymentProxy);
    process.env.PAYMENT_API_URL = 'https://api.payment.com';
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('issuePayment', () => {
    it('should handle API errors when success is false', async () => {
      const paymentRequest: PaymentRequestDto = {
        id: 'order-123',
        title: 'Test Payment',
        unit_price: 100,
        quantity: 1,
      };

      const mockResponse = {
        data: {
          success: false,
          error: 'Payment failed',
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      await expect(service.issuePayment(new OrderDto())).rejects.toThrow('Payment API error: Payment failed');
    });

    it('should handle network errors', async () => {
      const paymentRequest: PaymentRequestDto = {
        id: 'order-123',
        title: 'Test Payment',
        unit_price: 100,
        quantity: 1,
      };

      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      await expect(service.issuePayment(new OrderDto())).rejects.toThrow('Payment API error: Network error');
    });
  });
});
