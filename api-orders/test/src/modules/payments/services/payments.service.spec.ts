import { Test, TestingModule } from '@nestjs/testing';
import { OrderDto } from '../../../../../src/modules/orders/dto/order.dto';
import { PaymentProxy } from '../../../../../src/modules/payments/services/payment.proxy';
import { PaymentService } from '../../../../../src/modules/payments/services/payment.service';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentProxy: PaymentProxy;

  const mockPaymentProxy = {
    issuePayment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: PaymentProxy,
          useValue: mockPaymentProxy,
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    paymentProxy = module.get<PaymentProxy>(PaymentProxy);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('issuePayment', () => {
    it('should issue payment successfully', async () => {
      const mockResponse = {
        paymentUrl: 'https://payment-url.com',
        qrCode: 'https://qr-code.com',
      };

      mockPaymentProxy.issuePayment.mockResolvedValue(mockResponse);

      const result = await service.issuePayment(new OrderDto());

      expect(paymentProxy.issuePayment).toHaveBeenCalledWith(new OrderDto());
      expect(result).toEqual(mockResponse);
    });

    it('should throw VitolaException when paymentRequest is null', async () => {
      await expect(service.issuePayment(null as any)).rejects.toThrow(VitolaException);
    });

    it('should throw VitolaException when paymentRequest is undefined', async () => {
      await expect(service.issuePayment(undefined as any)).rejects.toThrow(VitolaException);
    });
  });
});
