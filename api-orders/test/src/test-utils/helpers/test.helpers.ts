import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { PaymentProxy } from '../../../../src/modules/payments/services/payment.proxy';
import { createMockModel } from '../mocks/mongoose.mock';

export class TestHelpers {
  /**
   * Cria um módulo de teste com providers mockados comuns
   */
  static async createTestingModule(providers: any[] = [], imports: any[] = []): Promise<TestingModule> {
    return Test.createTestingModule({
      imports,
      providers,
    }).compile();
  }

  /**
   * Cria mocks para o modelo do Mongoose
   */
  static createMongooseMocks(modelNames: string[]) {
    return modelNames.map((name) => ({
      provide: getModelToken(name),
      useValue: createMockModel(),
    }));
  }

  /**
   * Cria providers mockados para o módulo Orders
   */
  static createOrdersMockProviders() {
    return [
      ...this.createMongooseMocks(['Order']),
      {
        provide: PaymentProxy,
        useValue: {
          issuePayment: jest.fn().mockResolvedValue({ paymentUrl: 'https://test.com' }),
          createPayment: jest.fn(),
          getPaymentStatus: jest.fn(),
        },
      },
    ];
  }

  /**
   * Cria providers mockados para o módulo Products
   */
  static createProductsMockProviders() {
    return this.createMongooseMocks(['Product']);
  }

  /**
   * Cria providers mockados para o módulo Payments
   */
  static createPaymentsMockProviders() {
    return [
      {
        provide: PaymentProxy,
        useValue: {
          createPayment: jest.fn(),
          getPaymentStatus: jest.fn(),
          issuePayment: jest.fn(),
        },
      },
    ];
  }

  /**
   * Limpa todos os mocks
   */
  static clearAllMocks() {
    jest.clearAllMocks();
  }

  /**
   * Gera um ObjectId válido para testes
   */
  static generateValidObjectId(): string {
    return '507f1f77bcf86cd799439011';
  }

  /**
   * Valida se uma string é um ObjectId válido
   */
  static isValidObjectId(id: string): boolean {
    return /^[0-9a-fA-F]{24}$/.test(id);
  }
}
