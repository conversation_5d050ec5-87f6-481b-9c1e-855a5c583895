import { VitolaException } from '../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace jest {
    interface Matchers<R> {
      toBeVitolaException(errorCode?: string, message?: string): R;
      toHaveValidObjectId(): R;
      toBeValidDate(): R;
    }
  }
}

export const customMatchers = {
  toBeVitolaException(received: any, errorCode?: string, message?: string) {
    const pass = received instanceof VitolaException;

    if (!pass) {
      return {
        message: () => `expected ${received} to be an instance of VitolaException`,
        pass: false,
      };
    }

    if (errorCode && received.getErrorCode() !== errorCode) {
      return {
        message: () => `expected error code to be ${errorCode}, but got ${received.getErrorCode()}`,
        pass: false,
      };
    }

    if (message && received.getMessage() !== message) {
      return {
        message: () => `expected message to be ${message}, but got ${received.getMessage()}`,
        pass: false,
      };
    }

    return {
      message: () => `expected ${received} not to be a VitolaException`,
      pass: true,
    };
  },

  toHaveValidObjectId(received: any) {
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    const pass = typeof received === 'string' && objectIdRegex.test(received);

    return {
      message: () => (pass ? `expected ${received} not to be a valid ObjectId` : `expected ${received} to be a valid ObjectId`),
      pass,
    };
  },

  toBeValidDate(received: any) {
    const pass = received instanceof Date && !isNaN(received.getTime());

    return {
      message: () => (pass ? `expected ${received} not to be a valid Date` : `expected ${received} to be a valid Date`),
      pass,
    };
  },
};

// Registra os matchers customizados
beforeAll(() => {
  expect.extend(customMatchers);
});
