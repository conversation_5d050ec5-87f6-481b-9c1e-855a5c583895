name: Deploy .NET Microservice

on:
  push:
    branches: [ main ]

jobs:
  build-test-analyze-deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./dotnet-service

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup .NET Core
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '7.0.x'

      - name: Restore dependencies
        run: dotnet restore

      - name: Build and Test
        run: dotnet test --collect:"XPlat Code Coverage"

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v1.2
        with:
          projectBaseDir: ./dotnet-service
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Wait for SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@v1.1.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Build Docker image
        run: |
          docker build -t ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/dotnet-service:latest .

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Push to Amazon ECR
        run: |
          aws ecr get-login-password --region ${{ secrets.AWS_REGION }} | \
            docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com
          docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/dotnet-service:latest

      - name: Deploy to EKS
        run: |
          aws eks update-kubeconfig --region ${{ secrets.AWS_REGION }} --name ${{ secrets.CLUSTER_NAME }}
          kubectl apply -f ../../k8s/dotnet-service.yaml
