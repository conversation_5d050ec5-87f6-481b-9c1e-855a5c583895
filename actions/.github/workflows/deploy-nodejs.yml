name: Reusable Node.js Pipeline

on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      dockerfile_path:
        required: false
        default: 'dockerfiles/Dockerfile-node'
        type: string
    secrets:
      SONAR_TOKEN:
        required: true
      SONAR_HOST_URL:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_REGION:
        required: true
      AWS_ACCOUNT_ID:
        required: true
      CLUSTER_NAME:
        required: true
      DB_USERNAME:
        required: false
      DB_PASSWORD:
        required: false
      DATABASE_URL:
        required: false
      ORDERS_API_URL:
        required: false
      PAYMENT_API_URL:
        required: false
      COSTUMER_API_URL:
        required: false
      MERCADO_PAGO_ACCESSTOKEN: 
        required: false
      MONGO_URI:
        required: false

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build and test
        run: |
          yarn build
          yarn test:cov

      - name: SonarQube analysis
        uses: sonarsource/sonarqube-scan-action@v2.0.2
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: https://sonarcloud.io

      - name: Wait for Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@v1.1.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: https://sonarcloud.io

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Mocky Deploy
        run: echo "Deploy Realizado da aplicação Node.js"

      - name: Build Docker image
        run: |
          docker build -f ${{ inputs.dockerfile_path }} -t ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ inputs.service_name }}:latest .

      - name: AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      # - name: Push Docker image
      #   run: |
      #     aws ecr get-login-password --region ${{ secrets.AWS_REGION }} | \
      #       docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com
      #     docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ inputs.service_name }}:latest

      - name: Deploy to EKS
        run: echo "Deploy Realizado da aplicação Node.js ${{ inputs.service_name }}"
        # run: |
          # set -e

          # aws eks update-kubeconfig --region ${{ secrets.AWS_REGION }} --name ${{ secrets.CLUSTER_NAME }}

          # # Instala plugin de autenticação
          # curl -o aws-iam-authenticator https://amazon-eks.s3.us-west-2.amazonaws.com/1.27.5/2023-10-26/bin/linux/amd64/aws-iam-authenticator
          # chmod +x aws-iam-authenticator
          # sudo mv aws-iam-authenticator /usr/local/bin/

          # # Testa conectividade com o cluster
          # kubectl get nodes

          # # Cria namespace se necessário
          # kubectl create namespace vitola-lanches --dry-run=client -o yaml | kubectl apply -f - --validate=false

          # # Variáveis base64
          # export AWS_ACCOUNT_ID=${{ secrets.AWS_ACCOUNT_ID }}
          # export AWS_REGION=${{ secrets.AWS_REGION }}
          # export DATABASE_URL_BASE64=$(echo -n "${{ secrets.DATABASE_URL }}" | base64 -w 0)
          # export MONGO_URI_BASE64=$(echo -n "${{ secrets.MONGO_URI }}" | base64 -w 0)
          # export ORDERS_API_URL_BASE64=$(echo -n "${{ secrets.ORDERS_API_URL }}" | base64 -w 0)
          # export PAYMENT_API_URL_BASE64=$(echo -n "${{ secrets.PAYMENT_API_URL }}" | base64 -w 0)
          # export COSTUMER_API_URL_BASE64=$(echo -n "${{ secrets.COSTUMER_API_URL }}" | base64 -w 0)
          # export MERCADO_PAGO_ACCESSTOKEN_BASE64=$(echo -n "${{ secrets.MERCADO_PAGO_ACCESSTOKEN }}" | base64 -w 0)

          # # Apply secrets
          # if [ -f k8s/setup-secrets.yaml ]; then
          #   envsubst < k8s/setup-secrets.yaml | kubectl apply -f - --validate=false
          # fi

          # # Apply app manifest
          # envsubst < k8s/${{ inputs.service_name }}.yaml | kubectl apply -f - --validate=false

          # # Força reinício do deployment
          # kubectl rollout restart deployment/${{ inputs.service_name }} -n vitola-lanches

          # # Aguarda o deployment ficar disponível
          # echo "Waiting for ${{ inputs.service_name }} to be ready..."
          # kubectl wait --for=condition=available deployment/${{ inputs.service_name }} -n vitola-lanches --timeout=300s

          # # Aguarda readiness
          # echo "Waiting for ${{ inputs.service_name }} to be ready..."
          # kubectl wait --for=condition=ready pod -l app=${{ inputs.service_name }} -n vitola-lanches --timeout=300s
