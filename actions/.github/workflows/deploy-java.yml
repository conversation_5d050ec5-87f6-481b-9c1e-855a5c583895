name: Reusable Java Pipeline

on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      dockerfile_path:
        required: false
        default: 'dockerfiles/Dockerfile-java'
        type: string
    secrets:
      SONAR_TOKEN:
        required: true
      SONAR_HOST_URL:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_REGION:
        required: true
      AWS_ACCOUNT_ID:
        required: true
      CLUSTER_NAME:
        required: true
      DB_USERNAME:
        required: true
      DB_PASSWORD:
        required: true
      REDIS_PASSWORD:
        required: false
      SPRING_DATASOURCE_URL:
        required: true
      SPRING_REDIS_HOST:
        required: true
      SPRING_REDIS_PORT:
        required: false
      MYSQL_URI:
        required: false
      REDIS_URI:
        required: false
        

jobs:
  build-analyze-coverage:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build and test
        run: ./mvnw clean verify

      - name: SonarQube analysis
        uses: sonarsource/sonarqube-scan-action@v2.0.2
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: https://sonarcloud.io

      - name: Wait for Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@v1.1.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: https://sonarcloud.io

  deploy:
    runs-on: ubuntu-latest
    needs: build-analyze-coverage

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Mocky Deploy
        run: echo "Deploy Realizado da aplicação"

      # - name: Build Docker image
      #   run: |
      #     docker build -t ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ inputs.service_name }}:latest .

      - name: AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      # - name: Push Docker image
      #   run: |
      #     aws ecr get-login-password --region ${{ secrets.AWS_REGION }} | \
      #       docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com
      #     docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/${{ inputs.service_name }}:latest

      - name: Deploy to EKS
        run: echo "Deploy Realizado da aplicação Java ${{ inputs.service_name }}"
        # run: |
        #   aws eks update-kubeconfig --region ${{ secrets.AWS_REGION }} --name ${{ secrets.CLUSTER_NAME }}

        #   # Create namespace if it doesn't exist
        #   kubectl create namespace vitola-lanches --dry-run=client -o yaml | kubectl apply -f -

        #   # Replace environment variables in Kubernetes manifests
        #   export AWS_ACCOUNT_ID=${{ secrets.AWS_ACCOUNT_ID }}
        #   export AWS_REGION=${{ secrets.AWS_REGION }}
        #   export DB_USERNAME_BASE64=$(echo -n "${{ secrets.DB_USERNAME }}" | base64 -w 0)
        #   export DB_PASSWORD_BASE64=$(echo -n "${{ secrets.DB_PASSWORD }}" | base64 -w 0)
        #   export REDIS_PASSWORD_BASE64=$(echo -n "${{ secrets.REDIS_PASSWORD }}" | base64 -w 0)

        #   # Apply secrets first
        #   if [ -f k8s/setup-secrets.yaml ]; then
        #     envsubst < k8s/setup-secrets.yaml | kubectl apply -f -
        #   fi

        #   # Apply main application manifest
        #   envsubst < k8s/${{ inputs.service_name }}.yaml | kubectl apply -f -

        #   # Wait for application to be ready
        #   echo "Waiting for ${{ inputs.service_name }} to be ready..."
        #   kubectl wait --for=condition=ready pod -l app=${{ inputs.service_name }} -n vitola-lanches --timeout=300s
