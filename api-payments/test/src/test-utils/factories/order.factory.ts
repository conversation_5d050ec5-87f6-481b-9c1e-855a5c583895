import { OrderConfirmedDto } from '../../../../src/modules/payments/dto/order-confirmed.dto';
import { OrderItemDto } from '../../../../src/modules/payments/dto/order-item.dto';
import { OrderDto } from '../../../../src/modules/payments/dto/order.dto';
import { PaymentInformation } from '../../../../src/modules/payments/models/order-payment-information';
import { OrderStatus } from '../../../../src/modules/payments/models/order-status-enum';

export class OrderFactory {
  static createOrderItemDto(overrides: Partial<OrderItemDto> = {}): OrderItemDto {
    return {
      name: 'Misto Quente',
      description: 'Delicioso misto quente com queijo e presunto',
      quantity: 1,
      unitPrice: 15.5,
      ...overrides,
    };
  }

  static createOrderDto(overrides: Partial<OrderDto> = {}): OrderDto {
    const orderDto = new OrderDto();
    orderDto.id = '12345678900';
    orderDto.document = '12345678900';
    orderDto.items = [this.createOrderItemDto()];
    orderDto.total = 15.5;
    orderDto.status = OrderStatus.RECEIVED;
    orderDto.orderNumber = 987;
    orderDto.paymentInformation = this.createPaymentInformation();

    return {
      ...orderDto,
      ...overrides,
    };
  }

  static createOrderConfirmedDto(overrides: Partial<OrderConfirmedDto> = {}): OrderConfirmedDto {
    return {
      orderId: '12345678900',
      paymentProof: 'proof-123456',
      confirmed: true,
      totalPaid: 15.5,
      ...overrides,
    };
  }

  static createPaymentInformation(overrides: Partial<PaymentInformation> = {}): PaymentInformation {
    const paymentInfo = new PaymentInformation('https://qr-code-url.com', 'https://payment-url.com');
    paymentInfo.paymentProof = 'proof-123456';
    paymentInfo.totalPaid = 15.5;
    paymentInfo.confirmed = true;
    paymentInfo.updateDate = new Date();

    return {
      ...paymentInfo,
      ...overrides,
    };
  }

  static createMultipleOrderItems(count: number = 3): OrderItemDto[] {
    return Array.from({ length: count }, (_, index) =>
      this.createOrderItemDto({
        name: `Item ${index + 1}`,
        description: `Descrição do item ${index + 1}`,
        unitPrice: (index + 1) * 10,
      }),
    );
  }

  static createOrderWithMultipleItems(itemCount: number = 3): OrderDto {
    const items = this.createMultipleOrderItems(itemCount);
    const total = items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);

    return this.createOrderDto({
      items,
      total,
    });
  }
}
