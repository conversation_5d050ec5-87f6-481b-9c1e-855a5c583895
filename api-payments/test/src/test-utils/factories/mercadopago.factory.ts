import { PaymentRequestDto } from '../../../../src/modules/mercadopago/dto/payment-request.dto';
import { PaymentResponseDto } from '../../../../src/modules/mercadopago/dto/payment-response.dto';
import { generateNewObjectId } from '../../../../src/modules/shared/database/helpers/generate-objectId';

export class MercadoPagoFactory {
  static createPaymentRequestDto(overrides: Partial<PaymentRequestDto> = {}): PaymentRequestDto {
    const paymentRequest = new PaymentRequestDto(generateNewObjectId().toString(), 'Pedido: 123', 15.5, 1);

    return Object.assign(paymentRequest, overrides);
  }

  static createPaymentResponseDto(overrides: Partial<PaymentResponseDto> = {}): PaymentResponseDto {
    return {
      paymentUrl: 'https://www.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
      qrCode: 'https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
      ...overrides,
    };
  }

  static createMercadoPagoApiResponse(overrides: any = {}) {
    return {
      init_point: 'https://www.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
      sandbox_init_point: 'https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
      id: '123456789',
      collector_id: 987654321,
      operation_type: 'regular_payment',
      items: [
        {
          id: 'test-item-id',
          title: 'Test Item',
          quantity: 1,
          unit_price: 15.5,
          currency_id: 'BRL',
        },
      ],
      payment_methods: {
        excluded_payment_types: [{ id: 'ticket' }],
        installments: 12,
      },
      date_created: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      ...overrides,
    };
  }

  static createMercadoPagoError(message: string = 'Invalid payment data', statusCode: number = 400) {
    return {
      response: {
        status: statusCode,
        data: {
          message,
          error: 'bad_request',
          status: statusCode,
          cause: [
            {
              code: 'invalid_parameter',
              description: `Invalid parameter: ${message}`,
            },
          ],
        },
      },
      message: `Request failed with status code ${statusCode}`,
    };
  }
}
