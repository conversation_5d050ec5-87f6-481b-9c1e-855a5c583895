import { PaymentResponseDto } from '../../../../src/modules/mercadopago/dto/payment-response.dto';

export const mockMercadoPagoService = {
  createPayment: jest.fn(),
};

export const mockMercadoPagoResponse = {
  init_point: 'https://www.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
  sandbox_init_point: 'https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
  id: '123456789',
  collector_id: 987654321,
  operation_type: 'regular_payment',
  items: [
    {
      id: 'test-item-id',
      title: 'Test Item',
      quantity: 1,
      unit_price: 10.0,
      currency_id: 'BRL',
    },
  ],
  payment_methods: {
    excluded_payment_types: [{ id: 'ticket' }],
    installments: 12,
  },
  client_id: 'test-client-id',
  marketplace: 'NONE',
  marketplace_fee: 0,
  shipments: {
    mode: 'not_specified',
  },
  notification_url: null,
  additional_info: '',
  auto_return: 'approved',
  back_urls: {
    success: '',
    pending: '',
    failure: '',
  },
  date_created: new Date().toISOString(),
  last_updated: new Date().toISOString(),
  expires: false,
  expiration_date_from: null,
  expiration_date_to: null,
};

export const mockPaymentResponseDto: PaymentResponseDto = {
  paymentUrl: 'https://www.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
  qrCode: 'https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789',
};

export const mockMercadoPagoError = {
  response: {
    status: 400,
    data: {
      message: 'Invalid payment data',
      error: 'bad_request',
      status: 400,
      cause: [
        {
          code: 'invalid_parameter',
          description: 'Invalid parameter: unit_price',
        },
      ],
    },
  },
  message: 'Request failed with status code 400',
};
