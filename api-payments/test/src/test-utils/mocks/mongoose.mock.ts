import { Model } from 'mongoose';

export const createMockModel = <T>(): jest.Mocked<Model<T>> => {
  const mockModel = jest.fn().mockImplementation((data: any) => ({
    ...data,
    _id: data._id || 'mock-id',
    save: jest.fn().mockResolvedValue(data),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOneAndUpdate: jest.fn(),
    findOneAndDelete: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    deleteOne: jest.fn(),
    deleteMany: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
    populate: jest.fn(),
    exec: jest.fn(),
    lean: jest.fn(),
    sort: jest.fn(),
    limit: jest.fn(),
    skip: jest.fn(),
    select: jest.fn(),
    where: jest.fn(),
    exists: jest.fn(),
    distinct: jest.fn(),
    estimatedDocumentCount: jest.fn(),
    watch: jest.fn(),
    validate: jest.fn(),
  });

  return mockModel as any;
};

export const mockUpdateResult = {
  acknowledged: true,
  matchedCount: 1,
  modifiedCount: 1,
  upsertedCount: 0,
  upsertedId: null,
};

export const mockUpdateResultNotFound = {
  acknowledged: true,
  matchedCount: 0,
  modifiedCount: 0,
  upsertedCount: 0,
  upsertedId: null,
};
