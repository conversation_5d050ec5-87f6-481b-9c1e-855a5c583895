import { Test, TestingModule } from '@nestjs/testing';
import MercadoPagoConfig, { Preference } from 'mercadopago';
import { MercadoPagoService } from '../../../../../src/modules/mercadopago/services/mercado-pago.service';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { PaymentRequestDto } from '../../../../../src/modules/mercadopago/dto/payment-request.dto';
import { PaymentResponseDto } from '../../../../../src/modules/mercadopago/dto/payment-response.dto';

jest.mock('mercadopago', () => {
  const mockPreference = {
    create: jest.fn(),
  };
  const mockMercadoPagoConfig = jest.fn();
  return {
    __esModule: true,
    default: mockMercadoPagoConfig,
    Preference: jest.fn(() => mockPreference),
  };
});

describe('MercadoPagoService', () => {
  let service: MercadoPagoService;
  let mockPreference: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MercadoPagoService],
    }).compile();

    service = module.get<MercadoPagoService>(MercadoPagoService);
    mockPreference = new Preference({} as MercadoPagoConfig);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPayment', () => {
    it('should throw an error if paymentInfo is null or undefined', async () => {
      await expect(service.createPayment(undefined)).rejects.toThrow(
        VitolaException.ofValidation('PAYMENT_DATA_INVALID', 'Dados inválidos para criação do pagamento.'),
      );

      await expect(service.createPayment(undefined)).rejects.toThrow(
        VitolaException.ofValidation('PAYMENT_DATA_INVALID', 'Dados inválidos para criação do pagamento.'),
      );
    });

    it('should throw an error if paymentInfo is invalid', async () => {
      const invalidPaymentInfo = new PaymentRequestDto('', '', -10, -2);
      await expect(service.createPayment(invalidPaymentInfo)).rejects.toThrow(VitolaException);
    });

    it('should create a payment successfully', async () => {
      const validPaymentInfo: PaymentRequestDto = {
        id: '123',
        title: 'Test Product',
        unit_price: 100,
        quantity: 1,
      };

      const mockResponse = {
        init_point: 'https://sandbox.mercadopago.com/checkout/v2/redirect?pref_id=12345',
        sandbox_init_point: 'https://sandbox.mercadopago.com/checkout/v2/redirect?pref_id=12345',
      };

      (mockPreference.create as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.createPayment(validPaymentInfo);

      expect(mockPreference.create).toHaveBeenCalledWith({
        body: {
          items: [
            {
              id: validPaymentInfo.id,
              title: validPaymentInfo.title,
              unit_price: validPaymentInfo.unit_price,
              quantity: validPaymentInfo.quantity,
              currency_id: 'BRL',
            },
          ],
          payment_methods: {
            excluded_payment_types: [{ id: 'ticket' }],
            installments: 12,
          },
        },
        requestOptions: {
          timeout: 5000,
        },
      });

      expect(result).toEqual(PaymentResponseDto.of(mockResponse.init_point, mockResponse.sandbox_init_point));
    });

    it('should handle errors from Mercado Pago', async () => {
      const validPaymentInfo: PaymentRequestDto = { id: '123', title: 'Test Product', unit_price: 100, quantity: 1 };
      (mockPreference.create as jest.Mock).mockRejectedValue({ response: { data: { message: 'Test Error' } } });
      await expect(service.createPayment(validPaymentInfo)).rejects.toThrow(
        VitolaException.ofError('PAYMENT_DATA_ERROR', 'Erro ao criar pagamento: Test Error'),
      );
    });

    it('should handle generic errors from Mercado Pago', async () => {
      const validPaymentInfo: PaymentRequestDto = { id: '123', title: 'Test Product', unit_price: 100, quantity: 1 };
      (mockPreference.create as jest.Mock).mockRejectedValue({});
      await expect(service.createPayment(validPaymentInfo)).rejects.toThrow(
        VitolaException.ofError('PAYMENT_DATA_ERROR', 'Erro ao criar pagamento: Erro desconhecido ao criar o pagamento no Mercado Pago.'),
      );
    });
  });
});
