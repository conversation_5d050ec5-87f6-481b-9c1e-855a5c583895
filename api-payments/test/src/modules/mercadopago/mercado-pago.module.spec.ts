import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { MercadoPagoServicePortToken } from '../../../../src/modules/mercadopago/constants/mercado-pago.constants';
import { MercadoPagoModule } from '../../../../src/modules/mercadopago/mercado-pago.module';
import { MercadoPagoService } from '../../../../src/modules/mercadopago/services/mercado-pago.service';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';

describe('MercadoPagoModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [MercadoPagoModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  describe('module configuration', () => {
    it('should be defined', () => {
      expect(module).toBeDefined();
    });

    it('should not have any controllers', () => {
      const controllers = Reflect.getMetadata('controllers', MercadoPagoModule) || [];
      expect(controllers).toHaveLength(0);
    });

    it('should have correct number of providers', () => {
      const providers = Reflect.getMetadata('providers', MercadoPagoModule) || [];
      expect(providers).toHaveLength(1);
    });

    it('should have correct exports', () => {
      const exports = Reflect.getMetadata('exports', MercadoPagoModule) || [];
      expect(exports).toHaveLength(1);
      expect(exports).toContain(MercadoPagoServicePortToken);
    });
  });

  describe('provider configuration', () => {
    it('should configure MercadoPagoService with correct token', () => {
      const providers = Reflect.getMetadata('providers', MercadoPagoModule) || [];
      const mercadoPagoProvider = providers.find(
        (provider: any) => provider.provide === MercadoPagoServicePortToken && provider.useClass === MercadoPagoService,
      );

      expect(mercadoPagoProvider).toBeDefined();
      expect(mercadoPagoProvider.provide).toBe(MercadoPagoServicePortToken);
      expect(mercadoPagoProvider.useClass).toBe(MercadoPagoService);
    });

    it('should be able to get MercadoPagoService instance', () => {
      const service = module.get(MercadoPagoServicePortToken);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(MercadoPagoService);
    });
  });

  describe('module imports', () => {
    it('should be importable by other modules', async () => {
      const testModule = await Test.createTestingModule({
        imports: [MercadoPagoModule],
        controllers: [],
        providers: [],
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });

    it('should work with application modules', async () => {
      class TestService {
        constructor(private mercadoPagoService: any) {}

        getService() {
          return this.mercadoPagoService;
        }
      }

      const testModule = await Test.createTestingModule({
        imports: [MercadoPagoModule],
        providers: [
          {
            provide: TestService,
            useFactory: (mercadoPagoService: any) => new TestService(mercadoPagoService),
            inject: [MercadoPagoServicePortToken],
          },
        ],
      }).compile();

      const testService = testModule.get<TestService>(TestService);
      expect(testService).toBeDefined();
      expect(testService.getService()).toBeDefined();

      await testModule.close();
    });

    it('should not conflict with multiple imports', async () => {
      const testModule = await Test.createTestingModule({
        imports: [MercadoPagoModule, MercadoPagoModule], // Importing twice
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });
  });

  describe('service availability', () => {
    it('should make MercadoPagoService available for injection', () => {
      const service = module.get(MercadoPagoServicePortToken);
      expect(service).toBeDefined();
      expect(typeof service.createPayment).toBe('function');
    });

    it('should export MercadoPagoService for other modules', () => {
      const exports = Reflect.getMetadata('exports', MercadoPagoModule) || [];
      expect(exports).toContain(MercadoPagoServicePortToken);
    });
  });

  describe('module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [MercadoPagoModule],
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [MercadoPagoModule],
      }).compile();

      await expect(testModule.close()).resolves.not.toThrow();
    });
  });

  describe('configuration dependencies', () => {
    it('should handle module compilation errors gracefully', async () => {
      await expect(
        Test.createTestingModule({
          imports: [MercadoPagoModule],
        }).compile(),
      ).resolves.toBeDefined();
    });
  });
});
