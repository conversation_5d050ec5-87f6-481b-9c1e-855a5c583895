import { OrderResponseDto } from '../../../../../src/modules/payments/dto/order-response.dto';
import { OrderStatus } from '../../../../../src/modules/payments/models/order-status-enum';

describe('OrderResponseDto', () => {
  describe('constructor', () => {
    it('should create OrderResponseDto with all properties', () => {
      const id = 'test-id';
      const orderNumber = 123;
      const orderStatus = OrderStatus.RECEIVED;
      const receivedDate = new Date();
      const lastUpdateDate = new Date();

      const orderResponse = new OrderResponseDto(id, orderNumber, orderStatus, receivedDate, lastUpdateDate);

      expect(orderResponse.id).toBe(id);
      expect(orderResponse.orderNumber).toBe(orderNumber);
      expect(orderResponse.orderStatus).toBe(orderStatus);
      expect(orderResponse.receivedDate).toBe(receivedDate);
      expect(orderResponse.lastUpdateDate).toBe(lastUpdateDate);
    });

    it('should handle different order statuses', () => {
      const statuses = [OrderStatus.RECEIVED, OrderStatus.COMPLETED];
      
      statuses.forEach(status => {
        const orderResponse = new OrderResponseDto('id', 123, status, new Date(), new Date());
        expect(orderResponse.orderStatus).toBe(status);
      });
    });

    it('should handle different data types correctly', () => {
      const orderResponse = new OrderResponseDto('string-id', 456, OrderStatus.RECEIVED, new Date(), new Date());

      expect(typeof orderResponse.id).toBe('string');
      expect(typeof orderResponse.orderNumber).toBe('number');
      expect(orderResponse.receivedDate).toBeInstanceOf(Date);
      expect(orderResponse.lastUpdateDate).toBeInstanceOf(Date);
    });
  });
});