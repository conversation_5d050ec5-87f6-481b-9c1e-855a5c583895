import { OrderDtoMockDocumentation } from '../../../../../src/modules/payments/constants/order.constants';
import { OrderDto } from '../../../../../src/modules/payments/dto/order.dto';
import { OrderStatus } from '../../../../../src/modules/payments/models/order-status-enum';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('OrderDto', () => {
  const createValidOrderDto = (overrides: Partial<OrderDto> = {}): OrderDto => ({
    ...OrderDtoMockDocumentation,
    ...overrides,
  });

  // Mock OrderItemDto.validate
  jest.mock('../../../../../src/modules/payments/dto/order-item.dto', () => ({
    OrderItemDto: {
      validate: jest.fn(),
    },
  }));

  describe('validate', () => {
    it('should validate a valid order successfully', () => {
      const validOrder = createValidOrderDto();

      expect(() => OrderDto.validate(validOrder)).not.toThrow();
      expect(validOrder.status).toBe(OrderStatus.RECEIVED);
      expect(validOrder.orderNumber).toBeGreaterThan(0);
      expect(validOrder.orderNumber).toBeLessThanOrEqual(1000);
    });

    it('should throw error for null order', () => {
      const nullOrder = null as any;

      expect(() => OrderDto.validate(nullOrder)).toThrow(VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.'));
    });

    it('should throw error for undefined order', () => {
      const undefinedOrder = undefined as any;

      expect(() => OrderDto.validate(undefinedOrder)).toThrow(VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.'));
    });

    it('should throw error for empty items array', () => {
      const orderWithEmptyItems = createValidOrderDto({ items: [] });

      expect(() => OrderDto.validate(orderWithEmptyItems)).toThrow(VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.'));
    });

    it('should throw error for undefined items', () => {
      const orderWithUndefinedItems = createValidOrderDto({ items: undefined });

      expect(() => OrderDto.validate(orderWithUndefinedItems)).toThrow(VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.'));
    });

    it('should throw error for null items', () => {
      const orderWithNullItems = createValidOrderDto({ items: null as any });

      expect(() => OrderDto.validate(orderWithNullItems)).toThrow(VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.'));
    });

    it('should calculate total correctly and ignore provided total', () => {
      const orderWithWrongTotal = createValidOrderDto({
        total: 999, // Este valor será ignorado
        items: [
          { name: 'Item 1', description: 'Desc 1', quantity: 2, unitPrice: 10.0 },
          { name: 'Item 2', description: 'Desc 2', quantity: 1, unitPrice: 15.0 },
        ],
      });

      OrderDto.validate(orderWithWrongTotal);

      // O total deve ser calculado automaticamente: (2 * 10) + (1 * 15) = 35
      expect(orderWithWrongTotal.total).toBe(35);
    });

    it('should set status to RECEIVED', () => {
      const order = createValidOrderDto({ status: 'PENDING' as any });

      OrderDto.validate(order);

      expect(order.status).toBe(OrderStatus.RECEIVED);
    });

    it('should generate random order number between 1 and 1000', () => {
      const order = createValidOrderDto();

      OrderDto.validate(order);

      expect(order.orderNumber).toBeGreaterThan(0);
      expect(order.orderNumber).toBeLessThanOrEqual(1000);
      expect(Number.isInteger(order.orderNumber)).toBe(true);
    });

    it('should calculate total with multiple items correctly', () => {
      const order = createValidOrderDto({
        items: [
          { name: 'Item 1', description: 'Desc 1', quantity: 2, unitPrice: 12.5 },
          { name: 'Item 2', description: 'Desc 2', quantity: 3, unitPrice: 8.75 },
          { name: 'Item 3', description: 'Desc 3', quantity: 1, unitPrice: 15.0 },
        ],
      });

      OrderDto.validate(order);

      // (2 * 12.50) + (3 * 8.75) + (1 * 15.00) = 25 + 26.25 + 15 = 66.25
      expect(order.total).toBe(66.25);
    });

    it('should handle decimal calculations correctly', () => {
      const order = createValidOrderDto({
        items: [{ name: 'Item 1', description: 'Desc 1', quantity: 3, unitPrice: 7.33 }],
      });

      OrderDto.validate(order);

      expect(order.total).toBeCloseTo(21.99, 2);
    });
  });

  describe('validatePayment', () => {
    it('should throw error when paymentInformation is undefined', () => {
      const order = createValidOrderDto({ paymentInformation: undefined });

      expect(() => OrderDto.validatePayment(order)).toThrow(VitolaException.ofValidation('PAYMENT_NOT_FOUND', 'Pagamento do pedido é obrigatório.'));
    });

    it('should throw error when paymentInformation is null', () => {
      const order = createValidOrderDto({ paymentInformation: null as any });

      expect(() => OrderDto.validatePayment(order)).toThrow(VitolaException.ofValidation('PAYMENT_NOT_FOUND', 'Pagamento do pedido é obrigatório.'));
    });

    it('should validate paymentInformation when present', () => {
      const PaymentInformation = require('../../../../../src/modules/payments/models/order-payment-information').PaymentInformation;
      const validateSpy = jest.spyOn(PaymentInformation, 'validate');

      const order = createValidOrderDto();

      expect(() => OrderDto.validatePayment(order)).not.toThrow();
      expect(validateSpy).toHaveBeenCalledWith(order.paymentInformation);

      validateSpy.mockRestore();
    });
  });

  describe('DTO Structure', () => {
    it('should have items with correct structure', () => {
      const order = createValidOrderDto();

      expect(order.items).toBeInstanceOf(Array);
      expect(order.items[0]).toHaveProperty('name');
      expect(order.items[0]).toHaveProperty('description');
      expect(order.items[0]).toHaveProperty('quantity');
      expect(order.items[0]).toHaveProperty('unitPrice');
    });

    it('should handle string and number types correctly', () => {
      const order = createValidOrderDto();

      expect(typeof order.id).toBe('string');
      expect(typeof order.document).toBe('string');
      expect(typeof order.orderNumber).toBe('number');
      expect(typeof order.total).toBe('number');
      expect(typeof order.items[0].name).toBe('string');
      expect(typeof order.items[0].quantity).toBe('number');
      expect(typeof order.items[0].unitPrice).toBe('number');
    });

    it('should have paymentInformation with correct structure', () => {
      const order = createValidOrderDto();

      expect(order.paymentInformation).toBeDefined();
      expect(order.paymentInformation).toHaveProperty('paymentMethod');
      expect(order.paymentInformation).toHaveProperty('paymentUrl');
      expect(order.paymentInformation).toHaveProperty('qrCodeUrl');
    });
  });
});
