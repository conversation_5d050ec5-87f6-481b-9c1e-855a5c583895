import { OrderItemDto } from '../../../../../src/modules/payments/dto/order-item.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('OrderItemDto', () => {
  const createValidOrderItem = (overrides: Partial<OrderItemDto> = {}): OrderItemDto => ({
    name: 'Misto Quente',
    description: 'Delicioso misto quente',
    quantity: 1,
    unitPrice: 15.5,
    ...overrides,
  });

  describe('validate', () => {
    it('should validate a valid order item successfully', () => {
      const validItem = createValidOrderItem();

      expect(() => OrderItemDto.validate(validItem)).not.toThrow();
    });

    it('should throw error when name is missing', () => {
      const itemWithoutName = createValidOrderItem({ name: undefined });

      expect(() => OrderItemDto.validate(itemWithoutName)).toThrow(VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.'));
    });

    it('should throw error when name is empty string', () => {
      const itemWithEmptyName = createValidOrderItem({ name: '' });

      expect(() => OrderItemDto.validate(itemWithEmptyName)).toThrow(VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.'));
    });

    it('should throw error when description is missing', () => {
      const itemWithoutDescription = createValidOrderItem({ description: undefined });

      expect(() => OrderItemDto.validate(itemWithoutDescription)).toThrow(
        VitolaException.ofValidation('PRODUCT_DESCRIPTION_NOT_FOUND', 'Descrição do produto é obrigatório.'),
      );
    });

    it('should throw error when description is empty string', () => {
      const itemWithEmptyDescription = createValidOrderItem({ description: '' });

      expect(() => OrderItemDto.validate(itemWithEmptyDescription)).toThrow(
        VitolaException.ofValidation('PRODUCT_DESCRIPTION_NOT_FOUND', 'Descrição do produto é obrigatório.'),
      );
    });

    it('should throw error when quantity is missing', () => {
      const itemWithoutQuantity = createValidOrderItem({ quantity: undefined });

      expect(() => OrderItemDto.validate(itemWithoutQuantity)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.'),
      );
    });

    it('should throw error when quantity is zero', () => {
      const itemWithZeroQuantity = createValidOrderItem({ quantity: 0 });

      expect(() => OrderItemDto.validate(itemWithZeroQuantity)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.'),
      );
    });

    it('should throw error when quantity is negative', () => {
      const itemWithNegativeQuantity = createValidOrderItem({ quantity: -1 });

      expect(() => OrderItemDto.validate(itemWithNegativeQuantity)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.'),
      );
    });

    it('should throw error when unitPrice is missing', () => {
      const itemWithoutUnitPrice = createValidOrderItem({ unitPrice: undefined });

      expect(() => OrderItemDto.validate(itemWithoutUnitPrice)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.'),
      );
    });

    it('should throw error when unitPrice is zero', () => {
      const itemWithZeroUnitPrice = createValidOrderItem({ unitPrice: 0 });

      expect(() => OrderItemDto.validate(itemWithZeroUnitPrice)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.'),
      );
    });

    it('should throw error when unitPrice is negative', () => {
      const itemWithNegativeUnitPrice = createValidOrderItem({ unitPrice: -10 });

      expect(() => OrderItemDto.validate(itemWithNegativeUnitPrice)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.'),
      );
    });
  });
});
