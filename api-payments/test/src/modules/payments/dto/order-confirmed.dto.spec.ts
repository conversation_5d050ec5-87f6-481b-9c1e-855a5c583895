import { OrderConfirmedDto } from '../../../../../src/modules/payments/dto/order-confirmed.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('OrderConfirmedDto', () => {
  const createValidOrderConfirmed = (overrides: Partial<OrderConfirmedDto> = {}): OrderConfirmedDto => ({
    orderId: '12345678900',
    paymentProof: 'proof-123456',
    confirmed: true,
    totalPaid: 25.5,
    ...overrides,
  });

  describe('validate', () => {
    it('should validate a valid order confirmed successfully', () => {
      const validOrderConfirmed = createValidOrderConfirmed();

      expect(() => OrderConfirmedDto.validate(validOrderConfirmed)).not.toThrow();
    });

    it('should throw error when totalPaid is missing', () => {
      const orderWithoutTotalPaid = createValidOrderConfirmed({ totalPaid: undefined });

      expect(() => OrderConfirmedDto.validate(orderWithoutTotalPaid)).toThrow(
        VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido'),
      );
    });

    it('should throw error when totalPaid is null', () => {
      const orderWithNullTotalPaid = createValidOrderConfirmed({ totalPaid: null as any });

      expect(() => OrderConfirmedDto.validate(orderWithNullTotalPaid)).toThrow(
        VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido'),
      );
    });

    it('should throw error when totalPaid is zero', () => {
      const orderWithZeroTotalPaid = createValidOrderConfirmed({ totalPaid: 0 });

      expect(() => OrderConfirmedDto.validate(orderWithZeroTotalPaid)).toThrow(
        VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido'),
      );
    });

    it('should throw error when totalPaid is negative', () => {
      const orderWithNegativeTotalPaid = createValidOrderConfirmed({ totalPaid: -10 });

      expect(() => OrderConfirmedDto.validate(orderWithNegativeTotalPaid)).toThrow(
        VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido'),
      );
    });

    it('should accept positive totalPaid values', () => {
      const orderWithPositiveTotalPaid = createValidOrderConfirmed({ totalPaid: 100.75 });

      expect(() => OrderConfirmedDto.validate(orderWithPositiveTotalPaid)).not.toThrow();
    });
  });
});
