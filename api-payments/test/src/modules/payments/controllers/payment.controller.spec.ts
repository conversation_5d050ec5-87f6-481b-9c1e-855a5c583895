import { Test, TestingModule } from '@nestjs/testing';
import { PaymentResponseDto } from '../../../../../src/modules/mercadopago/dto/payment-response.dto';
import { PaymentController } from '../../../../../src/modules/payments/controllers/payment.controller';
import { OrderDto } from '../../../../../src/modules/payments/dto/order.dto';
import { PaymentServicePortToken } from '../../../../../src/modules/payments/constants/order.constants';

// Mock interfaces
interface MockPaymentServicePort {
  issue: jest.Mock;
}

// Mock factories
const createOrderDto = (overrides: Partial<OrderDto> = {}): OrderDto => ({
  id: 'test-order-id',
  document: '12345678900',
  orderNumber: 123,
  total: 25.5,
  items: [
    {
      name: 'Test Item',
      description: 'Test Description',
      quantity: 1,
      unitPrice: 25.5,
    },
  ],
  paymentInformation: {
    paymentMethod: 'QRCODEPIX' as any,
    paymentUrl: 'https://test-payment-url.com',
    qrCodeUrl: 'https://test-qr-code.com',
    paymentProof: 'test-proof',
    totalPaid: 25.5,
    confirmed: false,
    createDate: new Date(),
    updateDate: new Date(),
  } as any,
  status: 'RECEIVED' as any,
  ...overrides,
});

const createPaymentResponseDto = (overrides: Partial<PaymentResponseDto> = {}): PaymentResponseDto => ({
  paymentUrl: 'https://test-payment-url.com',
  qrCode: 'https://test-qr-code.com',
  ...overrides,
});

describe('PaymentController', () => {
  let controller: PaymentController;
  let paymentService: MockPaymentServicePort;

  beforeEach(async () => {
    const mockPaymentService: MockPaymentServicePort = {
      issue: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        {
          provide: PaymentServicePortToken,
          useValue: mockPaymentService,
        },
      ],
    }).compile();

    controller = module.get<PaymentController>(PaymentController);
    paymentService = module.get<MockPaymentServicePort>(PaymentServicePortToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('issuePayment', () => {
    it('should issue payment successfully', async () => {
      const orderDto: OrderDto = createOrderDto();
      const paymentResponse: PaymentResponseDto = createPaymentResponseDto();

      paymentService.issue.mockResolvedValue(paymentResponse);

      const result: PaymentResponseDto = await controller.issuePayment(orderDto);

      expect(result).toEqual(paymentResponse);
      expect(paymentService.issue).toHaveBeenCalledWith(orderDto);
    });

    it('should handle service errors', async () => {
      const orderDto = createOrderDto();
      const serviceError = new Error('Payment service error');

      paymentService.issue.mockRejectedValue(serviceError);

      await expect(controller.issuePayment(orderDto)).rejects.toThrow(serviceError);
      expect(paymentService.issue).toHaveBeenCalledWith(orderDto);
    });

    it('should pass order data correctly to service', async () => {
      const customOrder: OrderDto = createOrderDto({
        id: 'custom-order-id',
        document: '98765432100',
        orderNumber: 999,
        total: 150.75,
        items: [
          {
            name: 'Custom Item 1',
            quantity: 2,
            unitPrice: 50.25,
            description: '',
          },
          {
            name: 'Custom Item 2',
            quantity: 1,
            unitPrice: 50.25,
            description: '',
          },
        ],
      });
      const paymentResponse: PaymentResponseDto = createPaymentResponseDto();

      paymentService.issue.mockResolvedValue(paymentResponse);

      await controller.issuePayment(customOrder);

      expect(paymentService.issue).toHaveBeenCalledWith(customOrder);
    });

    it('should handle null order', async () => {
      const serviceError = new Error('Invalid order');

      paymentService.issue.mockRejectedValue(serviceError);

      await expect(controller.issuePayment(null as any)).rejects.toThrow();
      expect(paymentService.issue).toHaveBeenCalledWith(null);
    });

    it('should handle undefined order', async () => {
      const serviceError = new Error('Invalid order');

      paymentService.issue.mockRejectedValue(serviceError);

      await expect(controller.issuePayment(undefined as any)).rejects.toThrow();
      expect(paymentService.issue).toHaveBeenCalledWith(undefined);
    });

    it('should return payment response with correct structure', async () => {
      const orderDto: OrderDto = createOrderDto();
      const expectedResponse: PaymentResponseDto = createPaymentResponseDto({
        paymentUrl: 'https://custom-payment-url.com',
        qrCode: 'https://custom-qr-code.com',
      });

      paymentService.issue.mockResolvedValue(expectedResponse);

      const result: PaymentResponseDto = await controller.issuePayment(orderDto);

      expect(result).toHaveProperty('paymentUrl');
      expect(result).toHaveProperty('qrCode');
      expect(result.paymentUrl).toBe(expectedResponse.paymentUrl);
      expect(result.qrCode).toBe(expectedResponse.qrCode);
    });
  });

  describe('Controller Integration', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have payment service injected', () => {
      expect(paymentService).toBeDefined();
    });
  });
});
