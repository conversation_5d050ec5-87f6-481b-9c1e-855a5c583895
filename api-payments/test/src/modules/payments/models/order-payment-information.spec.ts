import { PaymentInformation } from '../../../../../src/modules/payments/models/order-payment-information';
import { PaymentMethod } from '../../../../../src/modules/payments/models/order-payment-method';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('PaymentInformation', () => {
  const createValidPaymentInfo = (overrides: Partial<PaymentInformation> = {}): PaymentInformation => {
    const paymentInfo = new PaymentInformation('https://qr-code.com', 'https://payment-url.com');
    paymentInfo.paymentProof = 'proof-123';
    paymentInfo.totalPaid = 25.50;
    return Object.assign(paymentInfo, overrides);
  };

  describe('constructor', () => {
    it('should create PaymentInformation with correct initial values', () => {
      const qrCodeUrl = 'https://qr-code.com';
      const paymentUrl = 'https://payment-url.com';
      
      const paymentInfo = new PaymentInformation(qrCodeUrl, paymentUrl);

      expect(paymentInfo.qrCodeUrl).toBe(qrCodeUrl);
      expect(paymentInfo.paymentUrl).toBe(paymentUrl);
      expect(paymentInfo.paymentMethod).toBe(PaymentMethod.QRCODEPIX);
      expect(paymentInfo.confirmed).toBe(false);
      expect(paymentInfo.createDate).toBeInstanceOf(Date);
    });
  });

  describe('of', () => {
    it('should create PaymentInformation from PaymentResponseDto', () => {
      const paymentResponseDto = {
        qrCode: 'https://qr-code.com',
        paymentUrl: 'https://payment-url.com',
      };

      const paymentInfo = PaymentInformation.of(paymentResponseDto as any);

      expect(paymentInfo.qrCodeUrl).toBe(paymentResponseDto.qrCode);
      expect(paymentInfo.paymentUrl).toBe(paymentResponseDto.paymentUrl);
      expect(paymentInfo.paymentMethod).toBe(PaymentMethod.QRCODEPIX);
      expect(paymentInfo.confirmed).toBe(false);
    });
  });

  describe('validate', () => {
    it('should validate a valid payment information successfully', () => {
      const validPaymentInfo = createValidPaymentInfo();

      expect(() => PaymentInformation.validate(validPaymentInfo)).not.toThrow();
    });

    it('should throw error when paymentMethod is missing', () => {
      const paymentInfoWithoutMethod = createValidPaymentInfo({ paymentMethod: undefined });

      expect(() => PaymentInformation.validate(paymentInfoWithoutMethod)).toThrow(
        VitolaException.ofValidation('PAYMENT_METHOD_NOT_FOUND', 'Forma de pagamento é obrigatória.')
      );
    });

    it('should throw error when qrCodeUrl is missing for QRCODEPIX payment', () => {
      const paymentInfoWithoutQrCode = createValidPaymentInfo({ 
        paymentMethod: PaymentMethod.QRCODEPIX,
        qrCodeUrl: undefined 
      });

      expect(() => PaymentInformation.validate(paymentInfoWithoutQrCode)).toThrow(
        VitolaException.ofValidation('QRCODE_NOT_FOUND', 'QRCode do pagamento é obrigatório.')
      );
    });

    it('should throw error when qrCodeUrl is empty for QRCODEPIX payment', () => {
      const paymentInfoWithEmptyQrCode = createValidPaymentInfo({ 
        paymentMethod: PaymentMethod.QRCODEPIX,
        qrCodeUrl: '' 
      });

      expect(() => PaymentInformation.validate(paymentInfoWithEmptyQrCode)).toThrow(
        VitolaException.ofValidation('QRCODE_NOT_FOUND', 'QRCode do pagamento é obrigatório.')
      );
    });

    it('should throw error when paymentProof is missing', () => {
      const paymentInfoWithoutProof = createValidPaymentInfo({ paymentProof: undefined });

      expect(() => PaymentInformation.validate(paymentInfoWithoutProof)).toThrow(
        VitolaException.ofValidation('PAYMENT_PROOF_NOT_FOUND', 'Comprovante de pagamento é obrigatória.')
      );
    });

    it('should throw error when paymentProof is empty', () => {
      const paymentInfoWithEmptyProof = createValidPaymentInfo({ paymentProof: '' });

      expect(() => PaymentInformation.validate(paymentInfoWithEmptyProof)).toThrow(
        VitolaException.ofValidation('PAYMENT_PROOF_NOT_FOUND', 'Comprovante de pagamento é obrigatória.')
      );
    });

    it('should throw error when totalPaid is missing', () => {
      const paymentInfoWithoutTotalPaid = createValidPaymentInfo({ totalPaid: undefined });

      expect(() => PaymentInformation.validate(paymentInfoWithoutTotalPaid)).toThrow(
        VitolaException.ofValidation('TOTAL_PAID_NOT_FOUND', 'Valor do pagamento é obrigatória.')
      );
    });

    it('should throw error when totalPaid is zero', () => {
      const paymentInfoWithZeroTotalPaid = createValidPaymentInfo({ totalPaid: 0 });

      expect(() => PaymentInformation.validate(paymentInfoWithZeroTotalPaid)).toThrow(
        VitolaException.ofValidation('TOTAL_PAID_NOT_FOUND', 'Valor do pagamento é obrigatória.')
      );
    });
  });
});