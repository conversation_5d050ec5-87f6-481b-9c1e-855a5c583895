import { Test, TestingModule } from '@nestjs/testing';
import { MercadoPagoServicePortToken } from '../../../../../../src/modules/mercadopago/constants/mercado-pago.constants';
import { PaymentRequestDto } from '../../../../../../src/modules/mercadopago/dto/payment-request.dto';
import { PaymentService } from '../../../../../../src/modules/payments/control/services/payment.service';
import { VitolaException } from '../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../../test-utils/factories/order.factory';

describe('PaymentService', () => {
  let service: PaymentService;
  let mockMercadoPagoService: any;

  beforeEach(async () => {
    mockMercadoPagoService = {
      createPayment: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: MercadoPagoServicePortToken,
          useValue: mockMercadoPagoService,
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('issue', () => {
    it('should validate order before processing payment', async () => {
      const invalidOrder = OrderFactory.createOrderDto({ items: [] });

      await expect(service.issue(invalidOrder)).rejects.toThrow();
    });

    it('should create payment successfully with valid order', async () => {
      const validOrder = OrderFactory.createOrderDto();
      const mockPaymentResponse = {
        paymentUrl: 'https://payment-url.com',
        qrCode: 'https://qr-code.com',
      };

      mockMercadoPagoService.createPayment.mockResolvedValue(mockPaymentResponse);

      const result = await service.issue(validOrder);

      expect(mockMercadoPagoService.createPayment).toHaveBeenCalledWith(expect.any(PaymentRequestDto));
      expect(result).toBe(mockPaymentResponse);
    });

    it('should handle MercadoPago service errors', async () => {
      const validOrder = OrderFactory.createOrderDto();
      const mercadoPagoError = new Error('MercadoPago API Error');

      mockMercadoPagoService.createPayment.mockRejectedValue(mercadoPagoError);

      await expect(service.issue(validOrder)).rejects.toThrow(VitolaException.ofValidation('ORDER_PAYMENT_ERROR', 'Ocorreu um erro ao criar pedido.'));
    });

    it('should log error when MercadoPago fails', async () => {
      const validOrder = OrderFactory.createOrderDto();
      const mercadoPagoError = new Error('MercadoPago API Error');
      const logSpy = jest.spyOn(service['logger'], 'log');

      mockMercadoPagoService.createPayment.mockRejectedValue(mercadoPagoError);

      try {
        await service.issue(validOrder);
      } catch (error) {
        // Expected error
      }

      expect(logSpy).toHaveBeenCalledWith(expect.stringContaining('Erro durante pagamento do pedido:'));
    });
  });
});
