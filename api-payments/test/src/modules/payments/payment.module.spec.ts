import { Test, TestingModule } from '@nestjs/testing';
import { MercadoPagoModule } from '../../../../src/modules/mercadopago/mercado-pago.module';
import { PaymentModule } from '../../../../src/modules/payments/payment.module';
import { PaymentServicePortToken } from '../../../../src/modules/payments/constants/order.constants';
import { PaymentService } from '../../../../src/modules/payments/control/services/payment.service';
import { PaymentController } from '../../../../src/modules/payments/controllers/payment.controller';
import { TestHelpers } from '../../test-utils/helpers/test.helpers';

describe('PaymentModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PaymentModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  describe('module configuration', () => {
    it('should be defined', () => {
      expect(module).toBeDefined();
    });

    it('should have correct number of controllers', () => {
      const controllers = Reflect.getMetadata('controllers', PaymentModule) || [];
      expect(controllers).toHaveLength(1);
      expect(controllers).toContain(PaymentController);
    });

    it('should have correct number of providers', () => {
      const providers = Reflect.getMetadata('providers', PaymentModule) || [];
      expect(providers).toHaveLength(1);
    });

    it('should have correct imports', () => {
      const imports = Reflect.getMetadata('imports', PaymentModule) || [];
      expect(imports).toHaveLength(1);
      expect(imports).toContain(MercadoPagoModule);
    });
  });

  describe('provider configuration', () => {
    it('should configure PaymentService with correct token', () => {
      const providers = Reflect.getMetadata('providers', PaymentModule) || [];
      const paymentProvider = providers.find((provider: any) => provider.provide === PaymentServicePortToken && provider.useClass === PaymentService);

      expect(paymentProvider).toBeDefined();
      expect(paymentProvider.provide).toBe(PaymentServicePortToken);
      expect(paymentProvider.useClass).toBe(PaymentService);
    });

    it('should be able to get PaymentService instance', () => {
      const service = module.get(PaymentServicePortToken);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(PaymentService);
    });
  });

  describe('controller configuration', () => {
    it('should be able to get PaymentController instance', () => {
      const controller = module.get<PaymentController>(PaymentController);
      expect(controller).toBeDefined();
      expect(controller).toBeInstanceOf(PaymentController);
    });
  });

  describe('module imports', () => {
    it('should be importable by other modules', async () => {
      const testModule = await Test.createTestingModule({
        imports: [PaymentModule],
        controllers: [],
        providers: [],
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });
  });

  describe('module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [PaymentModule],
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [PaymentModule],
      }).compile();

      await expect(testModule.close()).resolves.not.toThrow();
    });
  });
});
