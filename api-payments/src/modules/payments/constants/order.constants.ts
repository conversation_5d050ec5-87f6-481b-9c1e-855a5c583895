import { OrderResponseDto } from '../dto/order-response.dto';
import { OrderDto } from '../dto/order.dto';
import { PaymentMethod } from '../models/order-payment-method';
import { OrderStatus } from '../models/order-status-enum';

export const OrderRepositoryPortToken = Symbol('OrderRepositoryPort');
export const PaymentServicePortToken = Symbol('PaymentServicePortToken');

export const OrderDtoMockDocumentation: OrderDto = {
  id: `id`,
  document: '12345678900',
  items: [
    {
      name: 'Misto quente',
      description: 'Misto quente guloso',
      quantity: 1,
      unitPrice: 10,
    },
  ],
  paymentInformation: {
    paymentMethod: PaymentMethod.QRCODEPIX,
    paymentUrl: 'url-de-pagamento.com.br',
    qrCodeUrl: 'qr-code-url-de-pagamento.com.br',
    paymentProof: 'comprovante',
    totalPaid: 10,
    confirmed: true,
    createDate: new Date(),
    updateDate: new Date(),
  },
  total: 10,
  orderNumber: 987,
} as OrderDto;

export const OrderResponseDtoMockDocumentation: OrderResponseDto = {
  id: `id`,
  orderNumber: 123,
  orderStatus: OrderStatus.COMPLETED,
  receivedDate: new Date(),
  lastUpdateDate: new Date(),
};
