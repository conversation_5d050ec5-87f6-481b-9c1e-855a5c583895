import { ApiProperty } from '@nestjs/swagger';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
export class OrderConfirmedDto {
  @ApiProperty({
    description: 'Id do pedido',
    required: true,
    example: '12345678900',
  })
  orderId: string;

  paymentProof: string;

  @ApiProperty({
    description: 'Flag para pedido pago',
    required: true,
    example: true,
  })
  confirmed: boolean;

  @ApiProperty({
    description: 'Valor total pago do pedido',
    required: true,
    example: 10,
  })
  totalPaid: number;

  static validate(orderConfirmed: OrderConfirmedDto) {
    if (!orderConfirmed?.totalPaid) {
      throw VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido');
    }

    if (orderConfirmed.totalPaid <= 0) {
      throw VitolaException.ofValidation('ORDER_TOTALPAID_INVALID', 'Total do pedido inválido');
    }
  }
}
