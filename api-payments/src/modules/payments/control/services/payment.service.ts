import { Inject, Injectable, Logger } from '@nestjs/common';
import { MercadoPagoServicePortToken } from '../../../mercadopago/constants/mercado-pago.constants';
import { PaymentRequestDto } from '../../../mercadopago/dto/payment-request.dto';
import { PaymentResponseDto } from '../../../mercadopago/dto/payment-response.dto';
import { MercadoPagoServicePort } from '../../../mercadopago/services/mercado-pago.service.port';
import { VitolaException } from '../../../shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderDto } from '../../dto/order.dto';
import { PaymentServicePort } from './payment.service.port';

@Injectable()
export class PaymentService implements PaymentServicePort {
  private readonly logger = new Logger(PaymentService.name);

  constructor(@Inject(MercadoPagoServicePortToken) private readonly mercadoPagoService: MercadoPagoServicePort) {}

  async issue(order: OrderDto): Promise<PaymentResponseDto> {
    if (!order) {
      throw VitolaException.ofValidation('ORDER_NOT_FOUND', 'Escolha ao menos 1 item para criar o pedido.');
    }

    OrderDto.validate(order);

    try {
      return await this.mercadoPagoService.createPayment(new PaymentRequestDto(order.id, `Pedido: ${order.orderNumber}`, order.total, order.items.length));
    } catch (exception) {
      this.logger.log(`Erro durante pagamento do pedido: ${exception}`);
      throw VitolaException.ofValidation('ORDER_PAYMENT_ERROR', 'Ocorreu um erro ao criar pedido.');
    }
  }
}
