import { Body, Controller, Inject, Post } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { PaymentResponseDto } from '../../mercadopago/dto/payment-response.dto';
import { OrderDtoMockDocumentation, PaymentServicePortToken } from '../constants/order.constants';
import { PaymentServicePort } from '../control/services/payment.service.port';
import { OrderDto } from '../dto/order.dto';

@ApiTags('Pedidos')
@Controller('payments')
export class PaymentController {
  constructor(@Inject(PaymentServicePortToken) private readonly paymentService: PaymentServicePort) {}

  @Post('issue')
  @ApiResponse({ status: 201, description: 'Pagamento realizado com sucesso', schema: { example: { success: true, return: OrderDtoMockDocumentation } } })
  async issuePayment(@Body() order: OrderDto): Promise<PaymentResponseDto> {
    return await this.paymentService.issue(order);
  }
}
