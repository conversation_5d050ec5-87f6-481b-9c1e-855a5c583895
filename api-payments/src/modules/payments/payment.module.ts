import { Module } from '@nestjs/common';
import { MercadoPagoModule } from '../mercadopago/mercado-pago.module';
import { PaymentServicePortToken } from './constants/order.constants';
import { PaymentService } from './control/services/payment.service';
import { PaymentController } from './controllers/payment.controller';

@Module({
  imports: [MercadoPagoModule],
  providers: [{ provide: PaymentServicePortToken, useClass: PaymentService }],
  controllers: [PaymentController],
})
export class PaymentModule {}
