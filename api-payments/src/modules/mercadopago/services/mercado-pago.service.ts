import { Injectable } from '@nestjs/common';
import MercadoPagoConfig, { Preference } from 'mercadopago';
import { PreferenceCreateData } from 'mercadopago/dist/clients/preference/create/types';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { PaymentRequestDto } from '../dto/payment-request.dto';
import { PaymentResponseDto } from '../dto/payment-response.dto';
import { MercadoPagoServicePort } from './mercado-pago.service.port';

@Injectable()
export class MercadoPagoService implements MercadoPagoServicePort {
  private readonly config: MercadoPagoConfig;
  private readonly payment: Preference;

  constructor() {
    this.config = new MercadoPagoConfig({ accessToken: process.env.MERCADO_PAGO_ACCESSTOKEN ?? '' });
    this.payment = new Preference(this.config);
  }

  async createPayment(paymentInfo?: PaymentRequestDto): Promise<PaymentResponseDto> {
    if (!paymentInfo) {
      throw VitolaException.ofValidation('PAYMENT_DATA_INVALID', 'Dados inválidos para criação do pagamento.');
    }

    PaymentRequestDto.validate(paymentInfo);

    const paymentData: PreferenceCreateData = {
      body: {
        items: [
          {
            id: paymentInfo.id,
            title: paymentInfo.title,
            unit_price: paymentInfo.unit_price,
            quantity: paymentInfo.quantity,
            currency_id: 'BRL',
          },
        ],
        payment_methods: {
          excluded_payment_types: [{ id: 'ticket' }],
          installments: 12,
        },
      },
      requestOptions: {
        timeout: 5000,
      },
    };

    try {
      const response = await this.payment.create(paymentData);
      return PaymentResponseDto.of(response.init_point, response.sandbox_init_point);
    } catch (error) {
      const errorMessage = error.response?.data?.message ?? 'Erro desconhecido ao criar o pagamento no Mercado Pago.';
      throw VitolaException.ofError('PAYMENT_DATA_ERROR', `Erro ao criar pagamento: ${errorMessage}`);
    }
  }
}
