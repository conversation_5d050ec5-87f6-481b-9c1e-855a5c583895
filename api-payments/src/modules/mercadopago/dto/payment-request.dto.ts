import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
export class PaymentRequestDto {
  id: string;
  title: string;
  unit_price: number;
  quantity: number;

  constructor(id: string, title: string, unit_price: number, quantity: number) {
    this.id = id;
    this.title = title;
    this.unit_price = unit_price;
    this.quantity = quantity;
  }

  static validate(paymentInfo: PaymentRequestDto) {
    if (!paymentInfo.title || paymentInfo.unit_price <= 0 || paymentInfo.quantity <= 0) {
      throw VitolaException.ofValidation('PAYMENT_DATA_INVALID', 'Dados inválidos para o pagamento.');
    }
  }
}
