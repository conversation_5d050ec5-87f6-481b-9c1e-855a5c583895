import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MercadoPagoServicePortToken } from './constants/mercado-pago.constants';
import { MercadoPagoService } from './services/mercado-pago.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
  ],
  providers: [{ provide: MercadoPagoServicePortToken, useClass: MercadoPagoService }],
  exports: [MercadoPagoServicePortToken],
})
export class MercadoPagoModule {}
