import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MercadoPagoModule } from './modules/mercadopago/mercado-pago.module';
import { PaymentModule } from './modules/payments/payment.module';
import { SharedApiModule } from './modules/shared/api/modules/shared-api.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MercadoPagoModule,
    SharedApiModule,
    PaymentModule,
  ],
})
export class AppModule {}
