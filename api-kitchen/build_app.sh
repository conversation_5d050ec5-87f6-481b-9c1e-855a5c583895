#!/bin/bash

# Build script for API Kitchen
# Builds the application and starts it with Docker

set -e

echo "🏗️  Building API Kitchen..."

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist/
rm -rf node_modules/.cache/ 2>/dev/null || true

# Install dependencies
echo "📦 Installing dependencies..."
yarn install --frozen-lockfile

# Build the application
echo "🔨 Building TypeScript..."
yarn build

# Verify build output
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully"

# Start with Docker
echo "🐳 Starting with Docker..."
docker compose up --build