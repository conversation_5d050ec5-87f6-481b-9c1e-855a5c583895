<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.service.spec.ts">
    <testCase name="AppService getApiInfo should be defined" duration="190"/>
    <testCase name="AppService getApiInfo should return API information object" duration="6"/>
    <testCase name="AppService getApiInfo should return correct API name" duration="6"/>
    <testCase name="AppService getApiInfo should return correct API version" duration="69"/>
    <testCase name="AppService getApiInfo should return correct API description" duration="14"/>
    <testCase name="AppService getApiInfo should return all required properties" duration="14"/>
    <testCase name="AppService getApiInfo should return correct endpoints" duration="18"/>
    <testCase name="AppService getApiInfo should return correct features array" duration="10"/>
    <testCase name="AppService getApiInfo should return features as array" duration="4"/>
    <testCase name="AppService getApiInfo should return consistent data on multiple calls" duration="8"/>
    <testCase name="AppService getApiInfo should return immutable object structure" duration="4"/>
    <testCase name="AppService getHealth should be defined" duration="16"/>
    <testCase name="AppService getHealth should return health check object" duration="3"/>
    <testCase name="AppService getHealth should return all required health properties" duration="8"/>
    <testCase name="AppService getHealth should return status as ok" duration="138"/>
    <testCase name="AppService getHealth should return valid timestamp" duration="32"/>
    <testCase name="AppService getHealth should return current timestamp" duration="27"/>
    <testCase name="AppService getHealth should return valid uptime" duration="11"/>
    <testCase name="AppService getHealth should return memory usage object" duration="17"/>
    <testCase name="AppService getHealth should return valid memory values" duration="16"/>
    <testCase name="AppService getHealth should return Node.js version" duration="7"/>
    <testCase name="AppService getHealth should return different timestamps on multiple calls" duration="14"/>
    <testCase name="AppService getHealth should return increasing uptime on multiple calls" duration="14"/>
    <testCase name="AppService Service Integration should be defined" duration="14"/>
    <testCase name="AppService Service Integration should be an instance of AppService" duration="6"/>
    <testCase name="AppService Service Integration should handle concurrent calls correctly" duration="7"/>
    <testCase name="AppService Service Integration should maintain performance under load" duration="74"/>
    <testCase name="AppService Service Integration should not throw errors on repeated calls" duration="9"/>
    <testCase name="AppService Edge Cases should handle getApiInfo without errors" duration="1"/>
    <testCase name="AppService Edge Cases should handle getHealth without errors" duration="4"/>
    <testCase name="AppService Edge Cases should return valid JSON serializable objects" duration="5"/>
    <testCase name="AppService Edge Cases should return objects that can be safely cloned" duration="6"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.controller.spec.ts">
    <testCase name="AppController getApiInfo should return API information" duration="132"/>
    <testCase name="AppController getApiInfo should call appService.getApiInfo" duration="9"/>
    <testCase name="AppController getApiInfo should return object with required properties" duration="12"/>
    <testCase name="AppController getHealth should return health check information" duration="3"/>
    <testCase name="AppController getHealth should call appService.getHealth" duration="7"/>
    <testCase name="AppController getHealth should return object with health properties" duration="6"/>
    <testCase name="AppController getHealth should return status ok" duration="5"/>
    <testCase name="AppController Controller Integration should be defined" duration="7"/>
    <testCase name="AppController Controller Integration should have appService injected" duration="8"/>
    <testCase name="AppController Controller Integration should handle multiple concurrent requests" duration="128"/>
    <testCase name="AppController Controller Integration should maintain consistent responses" duration="28"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/http/http-client.service.spec.ts">
    <testCase name="HttpClientService Given HttpClientService is initialized When configuring the service Then it should use default orders API URL if not configured" duration="43"/>
    <testCase name="HttpClientService Given HttpClientService is initialized When configuring the service Then it should use configured orders API URL" duration="8"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making GET requests Then it should successfully perform GET request" duration="26"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making GET requests Then it should handle GET request with config" duration="21"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making GET requests Then it should handle GET request errors" duration="19"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making POST requests Then it should successfully perform POST request" duration="14"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making POST requests Then it should handle POST request with config" duration="11"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making POST requests Then it should handle POST request errors" duration="13"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making PUT requests Then it should successfully perform PUT request" duration="16"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making PUT requests Then it should handle PUT request errors" duration="54"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making PATCH requests Then it should successfully perform PATCH request" duration="21"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making DELETE requests Then it should successfully perform DELETE request" duration="31"/>
    <testCase name="HttpClientService Given HttpClientService is ready When making DELETE requests Then it should handle DELETE request errors" duration="146"/>
    <testCase name="HttpClientService Given HttpClientService is ready When handling errors Then it should handle errors without response data" duration="51"/>
    <testCase name="HttpClientService Given HttpClientService is ready When handling errors Then it should handle errors with unknown message" duration="14"/>
    <testCase name="HttpClientService Given HttpClientService is ready When performing health check Then it should return true for successful health check" duration="22"/>
    <testCase name="HttpClientService Given HttpClientService is ready When performing health check Then it should return false for failed health check" duration="17"/>
    <testCase name="HttpClientService Given HttpClientService is ready When performing health check Then it should return false for non-200 status" duration="45"/>
    <testCase name="HttpClientService Given HttpClientService is ready When getting base URL Then it should return the configured base URL" duration="30"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/repositories/order.repository.spec.ts">
    <testCase name="OrderRepository Given OrderRepository is initialized When finding all orders Then it should successfully fetch orders from orders API" duration="48"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding all orders Then it should return empty array when no orders found" duration="6"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding all orders Then it should throw error when API call fails" duration="443"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding all orders Then it should handle API errors gracefully" duration="12"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When changing order status Then it should successfully change order status" duration="5"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When changing order status Then it should throw error when status change fails" duration="7"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When changing order status Then it should handle different order statuses" duration="13"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding order by ID Then it should successfully fetch order by ID" duration="9"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding order by ID Then it should return null when order not found" duration="16"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding order by ID Then it should throw error for non-404 errors" duration="14"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding order by ID Then it should handle network errors" duration="6"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding orders by status Then it should successfully fetch orders by status" duration="3"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding orders by status Then it should return empty array when no orders with status found" duration="4"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding orders by status Then it should handle all order statuses" duration="21"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When finding orders by status Then it should throw error when API call fails" duration="26"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When handling edge cases Then it should handle empty order ID gracefully" duration="6"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When handling edge cases Then it should handle special characters in order ID" duration="4"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When handling edge cases Then it should handle multiple orders with same status" duration="17"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When handling concurrent requests Then it should handle multiple simultaneous findOrders calls" duration="8"/>
    <testCase name="OrderRepository Given OrderRepository is initialized When handling concurrent requests Then it should handle multiple status changes for different orders" duration="4"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-item.dto.spec.ts">
    <testCase name="OrderItemDto Properties should have all required properties" duration="87"/>
    <testCase name="OrderItemDto Properties should accept valid values" duration="1"/>
    <testCase name="OrderItemDto validate should validate successfully with valid data" duration="3"/>
    <testCase name="OrderItemDto validate should throw error when name is missing" duration="119"/>
    <testCase name="OrderItemDto validate should throw error when name is null" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when description is missing" duration="4"/>
    <testCase name="OrderItemDto validate should throw error when description is null" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is zero" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is negative" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is zero" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is negative" duration="0"/>
    <testCase name="OrderItemDto validate should validate multiple valid items" duration="0"/>
    <testCase name="OrderItemDto validate should handle edge cases for valid values" duration="1"/>
    <testCase name="OrderItemDto validate should handle large values" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/utils/order-number-generator.spec.ts">
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate order number with correct format" duration="63"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate different numbers on consecutive calls" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate unique order numbers with high probability" duration="39"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should include current date in order number" duration="2"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should extract correct date from order number" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate positive numeric order number" duration="16"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate different numbers on consecutive calls" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate unique numeric order numbers with reasonable collision rate" duration="10"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers within reasonable range" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers with expected structure" duration="14"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should be cryptographically secure (statistical test)" duration="39"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should validate correct format" duration="0"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should reject invalid formats" duration="0"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should extract correct date from valid order number" duration="0"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should return null for invalid format" duration="1"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should handle edge dates correctly" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-response.dto.spec.ts">
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with all properties" duration="15"/>
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with different order statuses" duration="15"/>
    <testCase name="OrderResponseDto constructor should handle different order numbers" duration="3"/>
    <testCase name="OrderResponseDto constructor should handle different date values" duration="6"/>
    <testCase name="OrderResponseDto of static method should create OrderResponseDto from Order entity" duration="8"/>
    <testCase name="OrderResponseDto of static method should handle Order with all different statuses" duration="3"/>
    <testCase name="OrderResponseDto of static method should handle Order with different order numbers" duration="10"/>
    <testCase name="OrderResponseDto of static method should preserve date information from Order" duration="1"/>
    <testCase name="OrderResponseDto of static method should handle Order with complex data" duration="1"/>
    <testCase name="OrderResponseDto of static method should create multiple DTOs from multiple Orders" duration="7"/>
    <testCase name="OrderResponseDto of static method should handle Orders with different statuses in batch" duration="4"/>
    <testCase name="OrderResponseDto DTO Properties should have all required properties defined" duration="13"/>
    <testCase name="OrderResponseDto DTO Properties should have correct property types" duration="2"/>
    <testCase name="OrderResponseDto DTO Properties should handle edge cases for order numbers" duration="0"/>
    <testCase name="OrderResponseDto DTO Properties should handle empty string id" duration="0"/>
    <testCase name="OrderResponseDto DTO Properties should maintain immutability of date objects" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order.dto.spec.ts">
    <testCase name="OrderDto Properties should have all required properties" duration="30"/>
    <testCase name="OrderDto validate should validate successfully with valid data" duration="5"/>
    <testCase name="OrderDto validate should throw error when items are missing" duration="367"/>
    <testCase name="OrderDto validate should throw error when items array is null" duration="2"/>
    <testCase name="OrderDto validate should throw error when order is null" duration="2"/>
    <testCase name="OrderDto validate should validate all items in the order" duration="12"/>
    <testCase name="OrderDto validate should generate unique id for each order" duration="1"/>
    <testCase name="OrderDto validate should set status to RECEIVED" duration="1"/>
    <testCase name="OrderDto validate should generate secure order number" duration="3"/>
    <testCase name="OrderDto validate should generate unique order numbers" duration="1"/>
    <testCase name="OrderDto validatePayment should validate successfully with valid payment" duration="1"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is missing" duration="3"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is undefined" duration="16"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for single item" duration="1"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for multiple items" duration="4"/>
    <testCase name="OrderDto calculateTotal should return zero for empty items array" duration="1"/>
    <testCase name="OrderDto calculateTotal should handle decimal values correctly" duration="2"/>
    <testCase name="OrderDto Integration Tests should validate and calculate total in sequence" duration="1"/>
    <testCase name="OrderDto Integration Tests should handle complex order with multiple items" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/controllers/kitchen.controller.spec.ts">
    <testCase name="KitchenController findOrdersStatus should return list of orders successfully" duration="7"/>
    <testCase name="KitchenController findOrdersStatus should return empty array when no orders found" duration="3"/>
    <testCase name="KitchenController findOrdersStatus should handle service errors when finding orders" duration="62"/>
    <testCase name="KitchenController findOrdersStatus should return orders with different statuses" duration="4"/>
    <testCase name="KitchenController findOrdersStatus should return orders sorted by creation date" duration="2"/>
    <testCase name="KitchenController changeOrderStatus should change order status successfully" duration="3"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid order id" duration="7"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid status" duration="2"/>
    <testCase name="KitchenController changeOrderStatus should handle order not found" duration="2"/>
    <testCase name="KitchenController changeOrderStatus should handle service errors when changing status" duration="4"/>
    <testCase name="KitchenController changeOrderStatus should change status to all valid statuses" duration="3"/>
    <testCase name="KitchenController Controller Integration should be defined" duration="2"/>
    <testCase name="KitchenController Controller Integration should have kitchenService injected" duration="2"/>
    <testCase name="KitchenController Controller Integration should handle concurrent requests" duration="2"/>
  </file>
</testExecutions>