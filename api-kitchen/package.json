{"name": "api-kitchen", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=2", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:unit": "jest --config ./test/jest-unit.json", "test:unit:watch": "jest --config ./test/jest-unit.json --watch", "test:unit:cov": "jest --config ./test/jest-unit.json --coverage", "test:integration": "jest --config ./test/jest-e2e.json", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker build -t api-kitchen .", "docker:test": "docker run --rm api-kitchen npm run test:ci", "sonar:start": "docker-compose -f docker-compose-sonar.yml up -d", "sonar:stop": "docker-compose -f docker-compose-sonar.yml down", "sonar:scan": "./run_sonar.sh", "sonar:logs": "docker-compose -f docker-compose-sonar.yml logs -f sonarqube", "sonar:clean": "docker-compose -f docker-compose-sonar.yml down -v"}, "dependencies": {"@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.2.0", "axios": "^1.7.7", "joi": "^17.13.3", "mongoose": "^8.16.4", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "jest-sonar-reporter": "^2.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}