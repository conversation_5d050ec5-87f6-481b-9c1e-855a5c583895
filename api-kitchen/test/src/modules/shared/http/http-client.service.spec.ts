import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { HttpClientService } from '../../../../../src/modules/shared/http/http-client.service';
import { of, throwError } from 'rxjs';
import { AxiosResponse } from 'axios';

describe('HttpClientService', () => {
  let service: HttpClientService;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;

  const mockAxiosResponse = <T>(data: T): AxiosResponse<T> => ({
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {} as any,
  });

  beforeEach(async () => {
    const mockHttpService = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HttpClientService,
        { provide: HttpService, useValue: mockHttpService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<HttpClientService>(HttpClientService);
    httpService = module.get(HttpService);
    configService = module.get(ConfigService);
  });

  describe('Given HttpClientService is initialized', () => {
    describe('When configuring the service', () => {
      it('Then it should use default orders API URL if not configured', () => {
        configService.get.mockImplementation((key: string, defaultValue: string) => defaultValue);

        const newService = new HttpClientService(httpService, configService);

        expect(configService.get).toHaveBeenCalledWith('ORDERS_API_URL', 'http://localhost:3001');
        expect(newService.getBaseUrl()).toBe('http://localhost:3001');
      });

      it('Then it should use configured orders API URL', () => {
        configService.get.mockReturnValue('http://custom-api:3000');
        
        const newService = new HttpClientService(httpService, configService);
        
        expect(newService.getBaseUrl()).toBe('http://custom-api:3000');
      });
    });
  });

  describe('Given HttpClientService is ready', () => {
    beforeEach(async () => {
      configService.get.mockReturnValue('http://test-api:3001');

      // Recriar o serviço com a nova configuração
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HttpClientService,
          {
            provide: HttpService,
            useValue: httpService,
          },
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      service = module.get<HttpClientService>(HttpClientService);
    });

    describe('When making GET requests', () => {
      it('Then it should successfully perform GET request', (done) => {
        const mockData = { id: 1, name: 'Test' };
        httpService.get.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.get('/test-endpoint').subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/test-endpoint', undefined);
            done();
          }
        });
      });

      it('Then it should handle GET request with config', (done) => {
        const mockData = { success: true };
        const config = { headers: { 'Authorization': 'Bearer token' } };
        httpService.get.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.get('/secure-endpoint', config).subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/secure-endpoint', config);
            done();
          }
        });
      });

      it('Then it should handle GET request errors', (done) => {
        const errorResponse = {
          response: {
            status: 404,
            data: { message: 'Not found' }
          }
        };
        httpService.get.mockReturnValue(throwError(() => errorResponse));

        service.get('/not-found').subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(404);
            expect(error.message).toBe('Not found');
            expect(error.method).toBe('GET');
            expect(error.url).toBe('http://test-api:3001/not-found');
            done();
          }
        });
      });
    });

    describe('When making POST requests', () => {
      it('Then it should successfully perform POST request', (done) => {
        const mockData = { id: 1, created: true };
        const postData = { name: 'New Item' };
        httpService.post.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.post('/create', postData).subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.post).toHaveBeenCalledWith('http://test-api:3001/create', postData, undefined);
            done();
          }
        });
      });

      it('Then it should handle POST request with config', (done) => {
        const mockData = { success: true };
        const postData = { data: 'test' };
        const config = { timeout: 10000 };
        httpService.post.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.post('/endpoint', postData, config).subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.post).toHaveBeenCalledWith('http://test-api:3001/endpoint', postData, config);
            done();
          }
        });
      });

      it('Then it should handle POST request errors', (done) => {
        const errorResponse = {
          response: {
            status: 400,
            data: { message: 'Bad request' }
          }
        };
        httpService.post.mockReturnValue(throwError(() => errorResponse));

        service.post('/bad-request', {}).subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(400);
            expect(error.message).toBe('Bad request');
            expect(error.method).toBe('POST');
            done();
          }
        });
      });
    });

    describe('When making PUT requests', () => {
      it('Then it should successfully perform PUT request', (done) => {
        const mockData = { id: 1, updated: true };
        const putData = { name: 'Updated Item' };
        httpService.put.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.put('/update/1', putData).subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.put).toHaveBeenCalledWith('http://test-api:3001/update/1', putData, undefined);
            done();
          }
        });
      });

      it('Then it should handle PUT request errors', (done) => {
        const errorResponse = {
          response: {
            status: 500,
            data: { message: 'Internal server error' }
          }
        };
        httpService.put.mockReturnValue(throwError(() => errorResponse));

        service.put('/error', {}).subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(500);
            expect(error.message).toBe('Internal server error');
            done();
          }
        });
      });
    });

    describe('When making PATCH requests', () => {
      it('Then it should successfully perform PATCH request', (done) => {
        const mockData = { id: 1, patched: true };
        const patchData = { status: 'active' };
        httpService.patch.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.patch('/patch/1', patchData).subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.patch).toHaveBeenCalledWith('http://test-api:3001/patch/1', patchData, undefined);
            done();
          }
        });
      });
    });

    describe('When making DELETE requests', () => {
      it('Then it should successfully perform DELETE request', (done) => {
        const mockData = { deleted: true };
        httpService.delete.mockReturnValue(of(mockAxiosResponse(mockData)));

        service.delete('/delete/1').subscribe({
          next: (result) => {
            expect(result).toEqual(mockData);
            expect(httpService.delete).toHaveBeenCalledWith('http://test-api:3001/delete/1', undefined);
            done();
          }
        });
      });

      it('Then it should handle DELETE request errors', (done) => {
        const errorResponse = {
          response: {
            status: 403,
            data: { message: 'Forbidden' }
          }
        };
        httpService.delete.mockReturnValue(throwError(() => errorResponse));

        service.delete('/forbidden').subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(403);
            expect(error.message).toBe('Forbidden');
            done();
          }
        });
      });
    });

    describe('When handling errors', () => {
      it('Then it should handle errors without response data', (done) => {
        const errorResponse = {
          message: 'Network error'
        };
        httpService.get.mockReturnValue(throwError(() => errorResponse));

        service.get('/network-error').subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(500);
            expect(error.message).toBe('Network error');
            expect(error.timestamp).toBeDefined();
            done();
          }
        });
      });

      it('Then it should handle errors with unknown message', (done) => {
        const errorResponse = {};
        httpService.get.mockReturnValue(throwError(() => errorResponse));

        service.get('/unknown-error').subscribe({
          error: (error) => {
            expect(error.statusCode).toBe(500);
            expect(error.message).toBe('Unknown error');
            done();
          }
        });
      });
    });

    describe('When performing health check', () => {
      it('Then it should return true for successful health check', async () => {
        httpService.get.mockReturnValue(of(mockAxiosResponse({ status: 'ok' })));

        const result = await service.healthCheck();

        expect(result).toBe(true);
        expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/health');
      });

      it('Then it should return false for failed health check', async () => {
        httpService.get.mockReturnValue(throwError(() => new Error('Connection failed')));

        const result = await service.healthCheck();

        expect(result).toBe(false);
      });

      it('Then it should return false for non-200 status', async () => {
        const response = mockAxiosResponse({ status: 'error' });
        response.status = 500;
        httpService.get.mockReturnValue(of(response));

        const result = await service.healthCheck();

        expect(result).toBe(false);
      });
    });

    describe('When getting base URL', () => {
      it('Then it should return the configured base URL', () => {
        const result = service.getBaseUrl();
        expect(result).toBe('http://test-api:3001');
      });
    });
  });
});
