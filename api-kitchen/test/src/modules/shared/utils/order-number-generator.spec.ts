import {
  generateSecureOrderNumber,
  generateSecureNumericOrderNumber,
  isValidOrderNumberFormat,
  extractDateFromOrderNumber,
} from '../../../../../src/modules/shared/utils/order-number-generator';

describe('OrderNumberGenerator', () => {
  describe('generateSecureOrderNumber', () => {
    it('should generate order number with correct format', () => {
      const orderNumber = generateSecureOrderNumber();

      expect(orderNumber).toMatch(/^\d{8}-\d{6}$/);
      expect(isValidOrderNumberFormat(orderNumber)).toBe(true);
    });

    it('should generate different numbers on consecutive calls', () => {
      const orderNumber1 = generateSecureOrderNumber();
      const orderNumber2 = generateSecureOrderNumber();

      expect(orderNumber1).not.toBe(orderNumber2);
    });

    it('should generate unique order numbers with high probability', () => {
      const orderNumbers = new Set();
      const totalGenerated = 100; // Reduced from 1000 for more realistic test

      // Generate order numbers and check for uniqueness
      for (let i = 0; i < totalGenerated; i++) {
        const orderNumber = generateSecureOrderNumber();
        orderNumbers.add(orderNumber);
      }

      // With 1M possible combinations per day, 100 numbers should have very low collision rate
      // Allow for a small number of collisions (less than 5%)
      const uniqueCount = orderNumbers.size;
      const collisionRate = (totalGenerated - uniqueCount) / totalGenerated;

      expect(collisionRate).toBeLessThan(0.05); // Less than 5% collision rate
      expect(uniqueCount).toBeGreaterThan(95); // At least 95% unique
    });

    it('should include current date in order number', () => {
      const orderNumber = generateSecureOrderNumber();
      const today = new Date();
      const expectedDatePrefix = today.getFullYear().toString() + 
                                (today.getMonth() + 1).toString().padStart(2, '0') + 
                                today.getDate().toString().padStart(2, '0');
      
      expect(orderNumber.startsWith(expectedDatePrefix)).toBe(true);
    });

    it('should extract correct date from order number', () => {
      const orderNumber = generateSecureOrderNumber();
      const extractedDate = extractDateFromOrderNumber(orderNumber);
      const today = new Date();
      
      expect(extractedDate).not.toBeNull();
      expect(extractedDate!.getFullYear()).toBe(today.getFullYear());
      expect(extractedDate!.getMonth()).toBe(today.getMonth());
      expect(extractedDate!.getDate()).toBe(today.getDate());
    });
  });

  describe('generateSecureNumericOrderNumber', () => {
    it('should generate positive numeric order number', () => {
      const orderNumber = generateSecureNumericOrderNumber();

      expect(typeof orderNumber).toBe('number');
      expect(orderNumber).toBeGreaterThan(0);
      expect(Number.isInteger(orderNumber)).toBe(true);
    });

    it('should generate different numbers on consecutive calls', () => {
      const orderNumber1 = generateSecureNumericOrderNumber();
      const orderNumber2 = generateSecureNumericOrderNumber();

      // Should be different (very high probability with crypto random + hrtime)
      expect(orderNumber1).not.toBe(orderNumber2);
    });

    it('should generate unique numeric order numbers with reasonable collision rate', async () => {
      const orderNumbers = new Set();
      const totalGenerated = 50; // Reduced for numeric version due to timestamp component

      // Generate order numbers with small delays to reduce timestamp collisions
      for (let i = 0; i < totalGenerated; i++) {
        const orderNumber = generateSecureNumericOrderNumber();
        orderNumbers.add(orderNumber);

        // Small delay every 10 iterations to ensure timestamp changes
        if (i % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      // With timestamp + random component, expect reasonable uniqueness
      const uniqueCount = orderNumbers.size;
      const collisionRate = (totalGenerated - uniqueCount) / totalGenerated;

      expect(collisionRate).toBeLessThan(0.2); // Less than 20% collision rate
      expect(uniqueCount).toBeGreaterThan(40); // At least 80% unique
    });

    it('should generate numbers within reasonable range', () => {
      const orderNumber = generateSecureNumericOrderNumber();

      // Based on our algorithm: timestamp(5) + hrtime(2) + random(3) = up to 10 digits
      // Minimum: 0 * 100000 + 0 * 1000 + 0 = 0 (theoretical, very unlikely)
      // Maximum: 99999 * 100000 + 99 * 1000 + 999 ≈ 10,098,999
      expect(orderNumber).toBeGreaterThan(0); // Should be positive
      expect(orderNumber).toBeLessThan(11000000000); // Less than 11 billion (reasonable upper bound)
      expect(Number.isInteger(orderNumber)).toBe(true); // Should be integer
    });

    it('should generate numbers with expected structure', () => {
      // Generate multiple numbers to test the structure
      for (let i = 0; i < 10; i++) {
        const orderNumber = generateSecureNumericOrderNumber();

        // Should be a positive integer
        expect(orderNumber).toBeGreaterThan(0);
        expect(Number.isInteger(orderNumber)).toBe(true);

        // Should have reasonable length (1 to 10 digits based on our algorithm)
        const numberStr = orderNumber.toString();
        expect(numberStr.length).toBeGreaterThanOrEqual(1);
        expect(numberStr.length).toBeLessThanOrEqual(10);
      }
    });

    it('should be cryptographically secure (statistical test)', async () => {
      const orderNumbers: number[] = [];

      // Generate 50 order numbers with small delays
      for (let i = 0; i < 50; i++) {
        orderNumbers.push(generateSecureNumericOrderNumber());

        // Small delay every 5 iterations to ensure some timestamp variation
        if (i % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      // Check that most are different (allowing for some timestamp-based collisions)
      const uniqueNumbers = new Set(orderNumbers);
      expect(uniqueNumbers.size).toBeGreaterThan(40); // At least 80% unique

      // Check distribution of last digits (should be roughly uniform)
      const lastDigitCounts = new Array(10).fill(0);
      orderNumbers.forEach(num => {
        lastDigitCounts[num % 10]++;
      });

      // No digit should appear more than 15 times out of 50 (rough uniformity check)
      lastDigitCounts.forEach(count => {
        expect(count).toBeLessThanOrEqual(15);
      });
    });
  });

  describe('isValidOrderNumberFormat', () => {
    it('should validate correct format', () => {
      expect(isValidOrderNumberFormat('20241217-123456')).toBe(true);
      expect(isValidOrderNumberFormat('20250101-000001')).toBe(true);
      expect(isValidOrderNumberFormat('20231231-999999')).toBe(true);
    });

    it('should reject invalid formats', () => {
      expect(isValidOrderNumberFormat('2024121-123456')).toBe(false); // 7 digits date
      expect(isValidOrderNumberFormat('202412177-123456')).toBe(false); // 9 digits date
      expect(isValidOrderNumberFormat('20241217-12345')).toBe(false); // 5 digits suffix
      expect(isValidOrderNumberFormat('20241217-1234567')).toBe(false); // 7 digits suffix
      expect(isValidOrderNumberFormat('20241217_123456')).toBe(false); // Wrong separator
      expect(isValidOrderNumberFormat('20241217123456')).toBe(false); // No separator
      expect(isValidOrderNumberFormat('')).toBe(false); // Empty string
      expect(isValidOrderNumberFormat('invalid')).toBe(false); // Non-numeric
    });
  });

  describe('extractDateFromOrderNumber', () => {
    it('should extract correct date from valid order number', () => {
      const date = extractDateFromOrderNumber('20241217-123456');
      
      expect(date).not.toBeNull();
      expect(date!.getFullYear()).toBe(2024);
      expect(date!.getMonth()).toBe(11); // December (0-indexed)
      expect(date!.getDate()).toBe(17);
    });

    it('should return null for invalid format', () => {
      expect(extractDateFromOrderNumber('invalid')).toBeNull();
      expect(extractDateFromOrderNumber('2024121-123456')).toBeNull();
      expect(extractDateFromOrderNumber('')).toBeNull();
    });

    it('should handle edge dates correctly', () => {
      const newYear = extractDateFromOrderNumber('20250101-000001');
      expect(newYear!.getFullYear()).toBe(2025);
      expect(newYear!.getMonth()).toBe(0); // January
      expect(newYear!.getDate()).toBe(1);

      const endOfYear = extractDateFromOrderNumber('20241231-999999');
      expect(endOfYear!.getFullYear()).toBe(2024);
      expect(endOfYear!.getMonth()).toBe(11); // December
      expect(endOfYear!.getDate()).toBe(31);
    });
  });
});
