import { OrderItemDto } from '../../../../../src/modules/orders/dto/order-item.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('OrderItemDto', () => {
  describe('Properties', () => {
    it('should have all required properties', () => {
      const orderItem = new OrderItemDto();
      orderItem.name = 'Misto Quente';
      orderItem.description = 'Delicioso misto quente';
      orderItem.quantity = 2;
      orderItem.unitPrice = 15.5;

      expect(orderItem).toHaveProperty('name');
      expect(orderItem).toHaveProperty('description');
      expect(orderItem).toHaveProperty('quantity');
      expect(orderItem).toHaveProperty('unitPrice');
    });

    it('should accept valid values', () => {
      const orderItem = new OrderItemDto();
      orderItem.name = 'X-Burger';
      orderItem.description = 'Hambúrguer com queijo';
      orderItem.quantity = 3;
      orderItem.unitPrice = 25.99;

      expect(orderItem.name).toBe('X-Burger');
      expect(orderItem.description).toBe('Hambúrguer com queijo');
      expect(orderItem.quantity).toBe(3);
      expect(orderItem.unitPrice).toBe(25.99);
    });
  });

  describe('validate', () => {
    it('should validate successfully with valid data', () => {
      const validItem: OrderItemDto = {
        name: 'Misto Quente',
        description: 'Delicioso misto quente com queijo e presunto',
        quantity: 1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(validItem)).not.toThrow();
    });

    it('should throw error when name is missing', () => {
      const invalidItem: OrderItemDto = {
        name: '',
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.')
      );
    });

    it('should throw error when name is null', () => {
      const invalidItem: OrderItemDto = {
        name: null as any,
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.')
      );
    });

    it('should throw error when description is missing', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: '',
        quantity: 1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_DESCRIPTION_NOT_FOUND', 'Descrição do produto é obrigatório.')
      );
    });

    it('should throw error when description is null', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: null as any,
        quantity: 1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_DESCRIPTION_NOT_FOUND', 'Descrição do produto é obrigatório.')
      );
    });

    it('should throw error when quantity is missing', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: null as any,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.')
      );
    });

    it('should throw error when quantity is zero', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: 0,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.')
      );
    });

    it('should throw error when quantity is negative', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: -1,
        unitPrice: 15.5,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.')
      );
    });

    it('should throw error when unitPrice is missing', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: null as any,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.')
      );
    });

    it('should throw error when unitPrice is zero', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: 0,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.')
      );
    });

    it('should throw error when unitPrice is negative', () => {
      const invalidItem: OrderItemDto = {
        name: 'Nome válido',
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: -10,
      };

      expect(() => OrderItemDto.validate(invalidItem)).toThrow(
        VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.')
      );
    });

    it('should validate multiple valid items', () => {
      const validItems: OrderItemDto[] = [
        {
          name: 'Misto Quente',
          description: 'Misto quente tradicional',
          quantity: 1,
          unitPrice: 15.5,
        },
        {
          name: 'X-Burger',
          description: 'Hambúrguer especial',
          quantity: 2,
          unitPrice: 25.99,
        },
        {
          name: 'Refrigerante',
          description: 'Coca-Cola 350ml',
          quantity: 3,
          unitPrice: 5.0,
        },
      ];

      validItems.forEach(item => {
        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });
    });

    it('should handle edge cases for valid values', () => {
      const edgeCaseItem: OrderItemDto = {
        name: 'A',
        description: 'B',
        quantity: 1,
        unitPrice: 0.01,
      };

      expect(() => OrderItemDto.validate(edgeCaseItem)).not.toThrow();
    });

    it('should handle large values', () => {
      const largeValueItem: OrderItemDto = {
        name: 'Produto Caro',
        description: 'Produto muito caro',
        quantity: 999,
        unitPrice: 9999.99,
      };

      expect(() => OrderItemDto.validate(largeValueItem)).not.toThrow();
    });
  });
});
