import { OrderDto } from '../../../../../src/modules/orders/dto/order.dto';
import { OrderItemDto } from '../../../../../src/modules/orders/dto/order-item.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { PaymentInformation } from '../../../../../src/modules/orders/models/order-payment-information';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('OrderDto', () => {
  let validOrderItem: OrderItemDto;
  let validPaymentInfo: PaymentInformation;

  beforeEach(() => {
    validOrderItem = {
      name: 'Misto Quente',
      description: 'Delicioso misto quente',
      quantity: 2,
      unitPrice: 15.5,
    };

    validPaymentInfo = new PaymentInformation('https://qr.test', 'https://payment.test');
    validPaymentInfo.paymentProof = 'proof123';
    validPaymentInfo.totalPaid = 31.0;
  });

  describe('Properties', () => {
    it('should have all required properties', () => {
      const order = new OrderDto();
      order.id = 'test-id';
      order.document = '12345678900';
      order.items = [validOrderItem];
      order.paymentInformation = validPaymentInfo;
      order.total = 31.0;
      order.status = OrderStatus.RECEIVED;
      order.orderNumber = 123;

      expect(order).toHaveProperty('id');
      expect(order).toHaveProperty('document');
      expect(order).toHaveProperty('items');
      expect(order).toHaveProperty('paymentInformation');
      expect(order).toHaveProperty('total');
      expect(order).toHaveProperty('status');
      expect(order).toHaveProperty('orderNumber');
    });
  });

  describe('validate', () => {
    it('should validate successfully with valid data', () => {
      const order: OrderDto = {
        id: '',
        document: '12345678900',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      expect(() => OrderDto.validate(order)).not.toThrow();
      expect(order.id).toBeDefined();
      expect(order.status).toBe(OrderStatus.RECEIVED);
      expect(order.orderNumber).toBeGreaterThan(0);
      expect(order.total).toBe(31.0);
    });

    it('should throw error when items are missing', () => {
      const order: OrderDto = {
        id: '',
        items: [],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      expect(() => OrderDto.validate(order)).toThrow(
        VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.')
      );
    });

    it('should throw error when items array is null', () => {
      const order: OrderDto = {
        id: '',
        items: null as any,
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      expect(() => OrderDto.validate(order)).toThrow(
        VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.')
      );
    });

    it('should throw error when order is null', () => {
      expect(() => OrderDto.validate(null as any)).toThrow(
        VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.')
      );
    });

    it('should validate all items in the order', () => {
      const invalidItem: OrderItemDto = {
        name: '',
        description: 'Descrição válida',
        quantity: 1,
        unitPrice: 15.5,
      };

      const order: OrderDto = {
        id: '',
        items: [validOrderItem, invalidItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      expect(() => OrderDto.validate(order)).toThrow(
        VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.')
      );
    });

    it('should generate unique id for each order', () => {
      const order1: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      const order2: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      OrderDto.validate(order1);
      OrderDto.validate(order2);

      expect(order1.id).not.toBe(order2.id);
      expect(order1.id).toBeDefined();
      expect(order2.id).toBeDefined();
    });

    it('should set status to RECEIVED', () => {
      const order: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.PREPARING,
        orderNumber: 0,
      };

      OrderDto.validate(order);

      expect(order.status).toBe(OrderStatus.RECEIVED);
    });

    it('should generate secure order number', () => {
      const order: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      OrderDto.validate(order);

      expect(order.orderNumber).toBeGreaterThan(0);
      expect(typeof order.orderNumber).toBe('number');
      expect(order.orderNumber.toString()).toMatch(/^\d+$/); // Should be a positive integer
    });

    it('should generate unique order numbers', () => {
      const order1: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      const order2: OrderDto = {
        id: '',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 0,
      };

      OrderDto.validate(order1);
      OrderDto.validate(order2);

      expect(order1.orderNumber).not.toBe(order2.orderNumber);
    });
  });

  describe('validatePayment', () => {
    it('should validate successfully with valid payment', () => {
      const order: OrderDto = {
        id: 'test-id',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 31.0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      expect(() => OrderDto.validatePayment(order)).not.toThrow();
    });

    it('should throw error when payment information is missing', () => {
      const order: OrderDto = {
        id: 'test-id',
        items: [validOrderItem],
        paymentInformation: null as any,
        total: 31.0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      expect(() => OrderDto.validatePayment(order)).toThrow(
        VitolaException.ofValidation('PAYMENT_NOT_FOUND', 'Pagamento do pedido é obrigatório.')
      );
    });

    it('should throw error when payment information is undefined', () => {
      const order: OrderDto = {
        id: 'test-id',
        items: [validOrderItem],
        paymentInformation: undefined as any,
        total: 31.0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      expect(() => OrderDto.validatePayment(order)).toThrow(
        VitolaException.ofValidation('PAYMENT_NOT_FOUND', 'Pagamento do pedido é obrigatório.')
      );
    });
  });

  describe('calculateTotal', () => {
    it('should calculate total correctly for single item', () => {
      const order: OrderDto = {
        id: 'test-id',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      const total = OrderDto.calculateTotal(order);

      expect(total).toBe(31.0); // 2 * 15.5
    });

    it('should calculate total correctly for multiple items', () => {
      const item1: OrderItemDto = {
        name: 'Item 1',
        description: 'Descrição 1',
        quantity: 2,
        unitPrice: 10.0,
      };

      const item2: OrderItemDto = {
        name: 'Item 2',
        description: 'Descrição 2',
        quantity: 3,
        unitPrice: 5.0,
      };

      const order: OrderDto = {
        id: 'test-id',
        items: [item1, item2],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      const total = OrderDto.calculateTotal(order);

      expect(total).toBe(35.0); // (2 * 10.0) + (3 * 5.0)
    });

    it('should return zero for empty items array', () => {
      const order: OrderDto = {
        id: 'test-id',
        items: [],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      const total = OrderDto.calculateTotal(order);

      expect(total).toBe(0);
    });

    it('should handle decimal values correctly', () => {
      const item: OrderItemDto = {
        name: 'Item Decimal',
        description: 'Item com valores decimais',
        quantity: 3,
        unitPrice: 12.99,
      };

      const order: OrderDto = {
        id: 'test-id',
        items: [item],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.RECEIVED,
        orderNumber: 123,
      };

      const total = OrderDto.calculateTotal(order);

      expect(total).toBe(38.97); // 3 * 12.99
    });
  });

  describe('Integration Tests', () => {
    it('should validate and calculate total in sequence', () => {
      const order: OrderDto = {
        id: '',
        document: '12345678900',
        items: [validOrderItem],
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.PREPARING,
        orderNumber: 0,
      };

      OrderDto.validate(order);

      expect(order.id).toBeDefined();
      expect(order.status).toBe(OrderStatus.RECEIVED);
      expect(order.orderNumber).toBeGreaterThan(0);
      expect(order.total).toBe(31.0);
    });

    it('should handle complex order with multiple items', () => {
      const items: OrderItemDto[] = [
        {
          name: 'Misto Quente',
          description: 'Misto quente tradicional',
          quantity: 1,
          unitPrice: 15.5,
        },
        {
          name: 'Refrigerante',
          description: 'Coca-Cola 350ml',
          quantity: 2,
          unitPrice: 5.0,
        },
        {
          name: 'Batata Frita',
          description: 'Porção de batata frita',
          quantity: 1,
          unitPrice: 12.0,
        },
      ];

      const order: OrderDto = {
        id: '',
        document: '98765432100',
        items,
        paymentInformation: validPaymentInfo,
        total: 0,
        status: OrderStatus.PREPARING,
        orderNumber: 0,
      };

      OrderDto.validate(order);

      expect(order.total).toBe(37.5); // 15.5 + (2 * 5.0) + 12.0
      expect(order.status).toBe(OrderStatus.RECEIVED);
      expect(order.id).toBeDefined();
      expect(order.orderNumber).toBeGreaterThan(0);
    });
  });
});
