import { Test, TestingModule } from '@nestjs/testing';
import { OrderRepository, OrderDto } from '../../../../../src/modules/orders/control/repositories/order.repository';
import { HttpClientService } from '../../../../../src/modules/shared/http/http-client.service';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { of, throwError } from 'rxjs';

describe('OrderRepository', () => {
  let repository: OrderRepository;
  let httpClientService: jest.Mocked<HttpClientService>;

  const mockOrder: OrderDto = {
    _id: '507f1f77bcf86cd799439011',
    document: '12345678900',
    orderNumber: 123,
    createDate: new Date('2024-01-15T10:00:00Z'),
    updateDate: new Date('2024-01-15T10:00:00Z'),
    status: OrderStatus.RECEIVED,
    items: [
      {
        name: 'X-<PERSON>',
        description: 'Hambúrguer com queijo',
        quantity: 1,
        unitPrice: 25.90
      }
    ],
    total: 25.90
  };

  beforeEach(async () => {
    const mockHttpClientService = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderRepository,
        { provide: HttpClientService, useValue: mockHttpClientService },
      ],
    }).compile();

    repository = module.get<OrderRepository>(OrderRepository);
    httpClientService = module.get(HttpClientService);
  });

  describe('Given OrderRepository is initialized', () => {
    describe('When finding all orders', () => {
      it('Then it should successfully fetch orders from orders API', async () => {
        const mockOrders = [mockOrder];
        httpClientService.get.mockReturnValue(of(mockOrders));

        const result = await repository.findOrders();

        expect(result).toEqual(mockOrders);
        expect(httpClientService.get).toHaveBeenCalledWith('/orders');
      });

      it('Then it should return empty array when no orders found', async () => {
        httpClientService.get.mockReturnValue(of([]));

        const result = await repository.findOrders();

        expect(result).toEqual([]);
        expect(httpClientService.get).toHaveBeenCalledWith('/orders');
      });

      it('Then it should throw error when API call fails', async () => {
        const error = { message: 'Network error' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        await expect(repository.findOrders()).rejects.toThrow('Failed to fetch orders: Network error');
        expect(httpClientService.get).toHaveBeenCalledWith('/orders');
      });

      it('Then it should handle API errors gracefully', async () => {
        const error = { statusCode: 500, message: 'Internal server error' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        await expect(repository.findOrders()).rejects.toThrow('Failed to fetch orders: Internal server error');
      });
    });

    describe('When changing order status', () => {
      const orderId = '507f1f77bcf86cd799439011';
      const newStatus = OrderStatus.PREPARING;

      it('Then it should successfully change order status', async () => {
        httpClientService.put.mockReturnValue(of(true));

        const result = await repository.changeOrderStatus(orderId, newStatus);

        expect(result).toBe(true);
        expect(httpClientService.put).toHaveBeenCalledWith(`/orders/${orderId}/status`, { status: newStatus });
      });

      it('Then it should throw error when status change fails', async () => {
        const error = { message: 'Order not found' };
        httpClientService.put.mockReturnValue(throwError(() => error));

        await expect(repository.changeOrderStatus(orderId, newStatus))
          .rejects.toThrow('Failed to change order status: Order not found');
        expect(httpClientService.put).toHaveBeenCalledWith(`/orders/${orderId}/status`, { status: newStatus });
      });

      it('Then it should handle different order statuses', async () => {
        const statuses = [
          OrderStatus.RECEIVED,
          OrderStatus.PREPARING,
          OrderStatus.READY,
          OrderStatus.COMPLETED,
          OrderStatus.CANCELED,
          OrderStatus.DISCARDED
        ];

        httpClientService.put.mockReturnValue(of(true));

        for (const status of statuses) {
          const result = await repository.changeOrderStatus(orderId, status);
          expect(result).toBe(true);
          expect(httpClientService.put).toHaveBeenCalledWith(`/orders/${orderId}/status`, { status });
        }
      });
    });

    describe('When finding order by ID', () => {
      const orderId = '507f1f77bcf86cd799439011';

      it('Then it should successfully fetch order by ID', async () => {
        httpClientService.get.mockReturnValue(of(mockOrder));

        const result = await repository.findOrderById(orderId);

        expect(result).toEqual(mockOrder);
        expect(httpClientService.get).toHaveBeenCalledWith(`/orders/${orderId}`);
      });

      it('Then it should return null when order not found', async () => {
        const error = { statusCode: 404, message: 'Order not found' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        const result = await repository.findOrderById(orderId);

        expect(result).toBeNull();
        expect(httpClientService.get).toHaveBeenCalledWith(`/orders/${orderId}`);
      });

      it('Then it should throw error for non-404 errors', async () => {
        const error = { statusCode: 500, message: 'Internal server error' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        await expect(repository.findOrderById(orderId))
          .rejects.toThrow('Failed to fetch order: Internal server error');
      });

      it('Then it should handle network errors', async () => {
        const error = { message: 'Connection timeout' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        await expect(repository.findOrderById(orderId))
          .rejects.toThrow('Failed to fetch order: Connection timeout');
      });
    });

    describe('When finding orders by status', () => {
      const status = OrderStatus.PREPARING;

      it('Then it should successfully fetch orders by status', async () => {
        const mockOrders = [mockOrder];
        httpClientService.get.mockReturnValue(of(mockOrders));

        const result = await repository.findOrdersByStatus(status);

        expect(result).toEqual(mockOrders);
        expect(httpClientService.get).toHaveBeenCalledWith(`/orders?status=${status}`);
      });

      it('Then it should return empty array when no orders with status found', async () => {
        httpClientService.get.mockReturnValue(of([]));

        const result = await repository.findOrdersByStatus(status);

        expect(result).toEqual([]);
        expect(httpClientService.get).toHaveBeenCalledWith(`/orders?status=${status}`);
      });

      it('Then it should handle all order statuses', async () => {
        const statuses = [
          OrderStatus.RECEIVED,
          OrderStatus.PREPARING,
          OrderStatus.READY,
          OrderStatus.COMPLETED,
          OrderStatus.CANCELED,
          OrderStatus.DISCARDED
        ];

        httpClientService.get.mockReturnValue(of([mockOrder]));

        for (const orderStatus of statuses) {
          const result = await repository.findOrdersByStatus(orderStatus);
          expect(result).toEqual([mockOrder]);
          expect(httpClientService.get).toHaveBeenCalledWith(`/orders?status=${orderStatus}`);
        }
      });

      it('Then it should throw error when API call fails', async () => {
        const error = { message: 'Service unavailable' };
        httpClientService.get.mockReturnValue(throwError(() => error));

        await expect(repository.findOrdersByStatus(status))
          .rejects.toThrow('Failed to fetch orders by status: Service unavailable');
      });
    });

    describe('When handling edge cases', () => {
      it('Then it should handle empty order ID gracefully', async () => {
        httpClientService.get.mockReturnValue(of(null));

        const result = await repository.findOrderById('');

        expect(httpClientService.get).toHaveBeenCalledWith('/orders/');
      });

      it('Then it should handle special characters in order ID', async () => {
        const specialOrderId = 'order-123-abc_def';
        httpClientService.get.mockReturnValue(of(mockOrder));

        const result = await repository.findOrderById(specialOrderId);

        expect(result).toEqual(mockOrder);
        expect(httpClientService.get).toHaveBeenCalledWith(`/orders/${specialOrderId}`);
      });

      it('Then it should handle multiple orders with same status', async () => {
        const multipleOrders = [mockOrder, { ...mockOrder, _id: 'different-id' }];
        httpClientService.get.mockReturnValue(of(multipleOrders));

        const result = await repository.findOrdersByStatus(OrderStatus.PREPARING);

        expect(result).toHaveLength(2);
        expect(result).toEqual(multipleOrders);
      });
    });

    describe('When handling concurrent requests', () => {
      it('Then it should handle multiple simultaneous findOrders calls', async () => {
        httpClientService.get.mockReturnValue(of([mockOrder]));

        const promises = Array.from({ length: 5 }, () => repository.findOrders());
        const results = await Promise.all(promises);

        results.forEach(result => {
          expect(result).toEqual([mockOrder]);
        });
        expect(httpClientService.get).toHaveBeenCalledTimes(5);
      });

      it('Then it should handle multiple status changes for different orders', async () => {
        httpClientService.put.mockReturnValue(of(true));

        const orderIds = ['order1', 'order2', 'order3'];
        const promises = orderIds.map(id => 
          repository.changeOrderStatus(id, OrderStatus.PREPARING)
        );

        const results = await Promise.all(promises);

        results.forEach(result => {
          expect(result).toBe(true);
        });
        expect(httpClientService.put).toHaveBeenCalledTimes(3);
      });
    });
  });
});
