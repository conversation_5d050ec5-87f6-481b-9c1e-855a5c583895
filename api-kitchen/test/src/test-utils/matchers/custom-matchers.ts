import { OrderStatus } from '../../../../src/modules/orders/models/order-status';
import { OrderResponseDto } from '../../../../src/modules/orders/dto/order-response.dto';

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidOrderStatus(): R;
      toBeValidOrderResponseDto(): R;
      toBeValidObjectId(): R;
      toBeRecentDate(withinSeconds?: number): R;
    }
  }
}

expect.extend({
  toBeValidOrderStatus(received: any) {
    const validStatuses = Object.values(OrderStatus);
    const pass = validStatuses.includes(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid order status`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid order status. Valid statuses are: ${validStatuses.join(', ')}`,
        pass: false,
      };
    }
  },

  toBeValidOrderResponseDto(received: any) {
    const requiredProperties = ['id', 'orderNumber', 'orderStatus', 'receivedDate', 'lastUpdateDate'];
    const missingProperties = requiredProperties.filter(prop => !(prop in received));

    if (missingProperties.length === 0) {
      const validTypes = {
        id: 'string',
        orderNumber: 'number',
        orderStatus: 'string',
        receivedDate: 'object', // Date is an object
        lastUpdateDate: 'object', // Date is an object
      };

      const invalidTypes = Object.entries(validTypes).filter(([prop, expectedType]) => {
        const actualType = typeof received[prop];
        if (expectedType === 'object' && prop.includes('Date')) {
          return !(received[prop] instanceof Date);
        }
        return actualType !== expectedType;
      });

      if (invalidTypes.length === 0 && Object.values(OrderStatus).includes(received.orderStatus)) {
        return {
          message: () => `expected ${JSON.stringify(received)} not to be a valid OrderResponseDto`,
          pass: true,
        };
      } else {
        const typeErrors = invalidTypes.map(([prop, expectedType]) => 
          `${prop} should be ${expectedType} but got ${typeof received[prop]}`
        ).join(', ');
        
        const statusError = !Object.values(OrderStatus).includes(received.orderStatus) 
          ? `orderStatus should be a valid OrderStatus but got ${received.orderStatus}` 
          : '';

        const errors = [typeErrors, statusError].filter(Boolean).join(', ');

        return {
          message: () => `expected ${JSON.stringify(received)} to be a valid OrderResponseDto. Issues: ${errors}`,
          pass: false,
        };
      }
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid OrderResponseDto. Missing properties: ${missingProperties.join(', ')}`,
        pass: false,
      };
    }
  },

  toBeValidObjectId(received: any) {
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    const pass = typeof received === 'string' && objectIdRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid ObjectId`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid ObjectId (24 character hex string)`,
        pass: false,
      };
    }
  },

  toBeRecentDate(received: any, withinSeconds: number = 5) {
    if (!(received instanceof Date)) {
      return {
        message: () => `expected ${received} to be a Date object`,
        pass: false,
      };
    }

    const now = new Date();
    const diffInSeconds = Math.abs(now.getTime() - received.getTime()) / 1000;
    const pass = diffInSeconds <= withinSeconds;

    if (pass) {
      return {
        message: () => `expected ${received.toISOString()} not to be within ${withinSeconds} seconds of now`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received.toISOString()} to be within ${withinSeconds} seconds of now, but it was ${diffInSeconds.toFixed(2)} seconds ago`,
        pass: false,
      };
    }
  },
});

export {};
