// import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
// import { createMockModel } from '../mocks/mongoose.mock';

export class TestHelpers {
  /**
   * Cria um módulo de teste com providers mockados comuns
   */
  static async createTestingModule(providers: any[] = [], imports: any[] = []): Promise<TestingModule> {
    return Test.createTestingModule({
      imports,
      providers,
    }).compile();
  }

  /**
   * Cria mocks para o modelo do Mongoose
   */
  // static createMongooseMocks(modelNames: string[]) {
  //   return modelNames.map((name) => ({
  //     provide: getModelToken(name),
  //     useValue: createMockModel(),
  //   }));
  // }

  /**
   * Cria providers mockados para o módulo Orders da Kitchen
   */
  // static createKitchenOrdersMockProviders() {
  //   return [
  //     ...this.createMongooseMocks(['Order']),
  //   ];
  // }

  /**
   * Limpa todos os mocks
   */
  static clearAllMocks() {
    jest.clearAllMocks();
  }

  /**
   * Reseta todos os mocks
   */
  static resetAllMocks() {
    jest.resetAllMocks();
  }

  /**
   * Cria um spy para console.error para testes de logging
   */
  static spyOnConsoleError() {
    return jest.spyOn(console, 'error').mockImplementation(() => {});
  }

  /**
   * Cria um spy para console.log para testes de logging
   */
  static spyOnConsoleLog() {
    return jest.spyOn(console, 'log').mockImplementation(() => {});
  }

  /**
   * Aguarda um tempo específico (útil para testes assíncronos)
   */
  static async wait(ms: number = 100): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Cria um mock para process.env
   */
  static mockProcessEnv(envVars: Record<string, string>) {
    const originalEnv = process.env;
    process.env = { ...originalEnv, ...envVars };

    return () => {
      process.env = originalEnv;
    };
  }

  /**
   * Verifica se uma função foi chamada com argumentos específicos
   */
  static expectCalledWith(mockFn: jest.Mock, ...args: any[]) {
    expect(mockFn).toHaveBeenCalledWith(...args);
  }

  /**
   * Verifica se uma função foi chamada um número específico de vezes
   */
  static expectCalledTimes(mockFn: jest.Mock, times: number) {
    expect(mockFn).toHaveBeenCalledTimes(times);
  }

  /**
   * Cria um mock para o OrderRepository
   */
  static createOrderRepositoryMock() {
    return {
      findOrders: jest.fn(),
      changeOrderStatus: jest.fn(),
    };
  }

  /**
   * Cria um mock para o KitchenService
   */
  static createKitchenServiceMock() {
    return {
      findOrdersStatus: jest.fn(),
      changeOrderStatus: jest.fn(),
    };
  }

  /**
   * Cria dados de teste para diferentes cenários de erro
   */
  static createErrorScenarios() {
    return {
      invalidOrderId: '',
      invalidStatus: null as any,
      nonExistentOrderId: 'non-existent-id',
      databaseError: new Error('Database connection error'),
    };
  }

  /**
   * Verifica se um objeto é uma instância de Error
   */
  static isError(obj: any): obj is Error {
    return obj instanceof Error;
  }

  /**
   * Cria um mock para UpdateResult do MongoDB
   */
  static createUpdateResult(modifiedCount: number = 1, matchedCount: number = 1) {
    return {
      acknowledged: true,
      modifiedCount,
      upsertedId: null,
      upsertedCount: 0,
      matchedCount,
    };
  }
}
