import { OrderResponseDto } from '../../../../src/modules/orders/dto/order-response.dto';
import { OrderDto } from '../../../../src/modules/orders/dto/order.dto';
import { OrderItemDto } from '../../../../src/modules/orders/dto/order-item.dto';
import { OrderStatus } from '../../../../src/modules/orders/models/order-status';
import { generateNewObjectId } from '../../../../src/modules/shared/database/helpers/generate-objectId';
import { generateSecureNumericOrderNumber } from '../../../../src/modules/shared/utils/order-number-generator';

export class OrderFactory {
  static createOrderItem(overrides: Partial<OrderItemDto> = {}): Partial<OrderItemDto> {
    return {
      name: 'Misto Quente',
      description: 'Delicioso misto quente com queijo e presunto',
      quantity: 1,
      unitPrice: 15.5,
      ...overrides,
    };
  }

  static createOrder(overrides: Partial<OrderDto> = {}): OrderDto {
    const order = {
      id: generateNewObjectId().toString(),
      document: '12345678900',
      orderNumber: generateSecureNumericOrderNumber(),
      status: OrderStatus.RECEIVED,
      items: [this.createOrderItem() as OrderItemDto],
      total: 15.5,
      ...overrides,
    };

    return order as OrderDto;
  }

  static createOrderResponseDto(overrides: Partial<OrderResponseDto> = {}): OrderResponseDto {
    const order = this.createOrder();
    const orderResponse = OrderResponseDto.of(order);

    return {
      ...orderResponse,
      ...overrides,
    };
  }

  static createMultipleOrderItems(count: number = 3): Partial<OrderItemDto>[] {
    return Array.from({ length: count }, (_, index) =>
      this.createOrderItem({
        name: `Item ${index + 1}`,
        description: `Descrição do item ${index + 1}`,
        unitPrice: (index + 1) * 10,
      }),
    );
  }

  static createOrderWithMultipleItems(itemCount: number = 3): OrderDto {
    const items = this.createMultipleOrderItems(itemCount);
    const total = items.reduce((sum, item) => sum + (item.quantity || 1) * (item.unitPrice || 0), 0);

    return this.createOrder({
      items: items as OrderItemDto[],
      total,
    });
  }

  static createOrderWithStatus(status: OrderStatus): OrderDto {
    return this.createOrder({
      status,
    });
  }

  static createMultipleOrders(count: number = 3): OrderDto[] {
    return Array.from({ length: count }, (_, index) =>
      this.createOrder({
        orderNumber: index + 1,
        status: index % 2 === 0 ? OrderStatus.RECEIVED : OrderStatus.PREPARING,
      }),
    );
  }

  static createOrdersWithDifferentStatuses(): OrderDto[] {
    return [
      this.createOrderWithStatus(OrderStatus.RECEIVED),
      this.createOrderWithStatus(OrderStatus.PREPARING),
      this.createOrderWithStatus(OrderStatus.READY),
      this.createOrderWithStatus(OrderStatus.COMPLETED),
    ];
  }
}
