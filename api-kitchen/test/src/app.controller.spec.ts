import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from '../../src/app.controller';
import { AppService } from '../../src/app.service';

describe('AppController', () => {
  let controller: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    controller = module.get<AppController>(AppController);
    appService = module.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getApiInfo', () => {
    it('should return API information', () => {
      const expectedInfo = {
        name: 'API Kitchen - Vitola Lanches',
        version: '1.0.0',
        description: 'API para gerenciamento da cozinha do Vitola Lanches',
        endpoints: {
          orders: '/kitchen/orders',
          docs: '/api/docs',
          health: '/health'
        },
        features: [
          'Listagem de pedidos da cozinha',
          'Alteração de status dos pedidos',
          'Documentação interativa com Swagger'
        ]
      };

      jest.spyOn(appService, 'getApiInfo').mockReturnValue(expectedInfo);

      const result = controller.getApiInfo();

      expect(result).toEqual(expectedInfo);
      expect(appService.getApiInfo).toHaveBeenCalledTimes(1);
    });

    it('should call appService.getApiInfo', () => {
      const spy = jest.spyOn(appService, 'getApiInfo');
      
      controller.getApiInfo();
      
      expect(spy).toHaveBeenCalled();
    });

    it('should return object with required properties', () => {
      const result = controller.getApiInfo();

      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('endpoints');
      expect(result).toHaveProperty('features');
    });
  });

  describe('getHealth', () => {
    it('should return health check information', () => {
      const expectedHealth = {
        status: 'ok',
        timestamp: '2023-01-01T00:00:00.000Z',
        uptime: 100,
        memory: {
          rss: 1000000,
          heapTotal: 2000000,
          heapUsed: 1500000,
          external: 500000,
          arrayBuffers: 100000
        },
        version: 'v18.0.0'
      };

      jest.spyOn(appService, 'getHealth').mockReturnValue(expectedHealth);

      const result = controller.getHealth();

      expect(result).toEqual(expectedHealth);
      expect(appService.getHealth).toHaveBeenCalledTimes(1);
    });

    it('should call appService.getHealth', () => {
      const spy = jest.spyOn(appService, 'getHealth');
      
      controller.getHealth();
      
      expect(spy).toHaveBeenCalled();
    });

    it('should return object with health properties', () => {
      const result = controller.getHealth();

      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('uptime');
      expect(result).toHaveProperty('memory');
      expect(result).toHaveProperty('version');
    });

    it('should return status ok', () => {
      const result = controller.getHealth() as any;
      
      expect(result.status).toBe('ok');
    });
  });

  describe('Controller Integration', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have appService injected', () => {
      expect(appService).toBeDefined();
    });

    it('should handle multiple concurrent requests', async () => {
      const promises = Array.from({ length: 5 }, () => 
        Promise.all([
          controller.getApiInfo(),
          controller.getHealth()
        ])
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach(([apiInfo, health]) => {
        expect(apiInfo).toBeDefined();
        expect(health).toBeDefined();
      });
    });

    it('should maintain consistent responses', () => {
      const info1 = controller.getApiInfo();
      const info2 = controller.getApiInfo();
      
      expect(info1).toEqual(info2);
    });
  });
});
