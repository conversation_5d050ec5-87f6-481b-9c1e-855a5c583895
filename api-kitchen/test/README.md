# 🧪 Testes Unitários - API Kitchen

Este diretório contém todos os testes unitários da API Kitchen, seguindo o mesmo padrão estabelecido na api-payments.

## 📁 Estrutura de Diretórios

```
test/
├── src/
│   ├── modules/                    # Testes por módulo
│   │   └── orders/                 # Testes do módulo de pedidos
│   │       ├── controllers/        # Testes dos controllers
│   │       ├── control/
│   │       │   ├── services/       # Testes dos services
│   │       │   └── repositories/   # Testes dos repositories
│   │       └── dto/                # Testes dos DTOs
│   ├── test-utils/                 # Utilitários de teste
│   │   ├── factories/              # Factories para criação de dados de teste
│   │   ├── helpers/                # Helpers para testes
│   │   ├── mocks/                  # Mocks para dependências
│   │   ├── matchers/               # Matchers customizados do Jest
│   │   └── setup/                  # Configuração global dos testes
│   └── app.service.spec.ts         # Testes do AppService
├── app.e2e-spec.ts                 # Testes end-to-end
├── jest-e2e.json                   # Configuração Jest para E2E
└── README.md                       # Este arquivo
```

## 🚀 Scripts de Teste

### Testes Unitários
```bash
# Executar todos os testes unitários
npm run test

# Executar testes em modo watch
npm run test:watch

# Executar testes com coverage
npm run test:cov

# Executar testes em modo debug
npm run test:debug
```

### Testes E2E
```bash
# Executar testes end-to-end
npm run test:e2e
```

## 📋 Cobertura de Testes

### Controllers
- ✅ **KitchenController**: Testa todos os endpoints da API
  - `findOrdersStatus()`: Listagem de pedidos
  - `changeOrderStatus()`: Alteração de status dos pedidos
  - Tratamento de erros e validações
  - Testes de integração

### Services
- ✅ **KitchenService**: Testa a lógica de negócio
  - `findOrdersStatus()`: Busca e transformação de dados
  - `changeOrderStatus()`: Validações e delegação para repository
  - Tratamento de erros e casos extremos

### Repositories
- ✅ **OrderRepository**: Testa interações com banco de dados
  - `findOrders()`: Consultas com ordenação
  - `changeOrderStatus()`: Atualizações de status
  - Mocks do Mongoose e validações de ObjectId

### DTOs
- ✅ **OrderResponseDto**: Testa transformação de dados
  - Constructor e propriedades
  - Método estático `of()`
  - Validações de tipos e estrutura

### Services de Aplicação
- ✅ **AppService**: Testa funcionalidades básicas da aplicação
  - `getApiInfo()`: Informações da API
  - `getHealth()`: Health check

## 🛠️ Utilitários de Teste

### Factories
- **OrderFactory**: Criação de dados de teste para pedidos
  - `createOrder()`: Cria pedidos completos
  - `createOrderItem()`: Cria itens de pedido
  - `createOrderResponseDto()`: Cria DTOs de resposta
  - `createMultipleOrders()`: Cria múltiplos pedidos
  - `createOrdersWithDifferentStatuses()`: Pedidos com status variados

### Helpers
- **TestHelpers**: Utilitários comuns para testes
  - `createTestingModule()`: Criação de módulos de teste
  - `createMongooseMocks()`: Mocks do Mongoose
  - `createKitchenOrdersMockProviders()`: Providers mockados
  - `clearAllMocks()` / `resetAllMocks()`: Limpeza de mocks
  - Spies para console e utilitários de tempo

### Mocks
- **mongoose.mock**: Mock completo do Mongoose
  - Métodos de query (find, findOne, etc.)
  - Métodos de modificação (create, updateOne, etc.)
  - Chainable methods (sort, limit, etc.)

### Matchers Customizados
- `toBeValidOrderStatus()`: Valida status de pedido
- `toBeValidOrderResponseDto()`: Valida estrutura do DTO
- `toBeValidObjectId()`: Valida ObjectId do MongoDB
- `toBeRecentDate()`: Valida datas recentes

## 📊 Estatísticas dos Testes

```
Test Suites: 4 passed, 4 total
Tests:       63 passed, 63 total
Snapshots:   0 total
Time:        ~14s
```

### Distribuição por Módulo
- **Controllers**: 16 testes
- **Services**: 16 testes  
- **Repositories**: 15 testes
- **DTOs**: 16 testes

## 🎯 Padrões Seguidos

### Estrutura de Teste
1. **Arrange**: Configuração de dados e mocks
2. **Act**: Execução da função testada
3. **Assert**: Verificação dos resultados

### Nomenclatura
- Describe blocks descrevem a classe/método sendo testado
- It blocks descrevem o comportamento esperado
- Nomes descritivos e em português

### Cobertura
- Casos de sucesso
- Casos de erro
- Validações de entrada
- Casos extremos (edge cases)
- Testes de integração

### Mocks e Stubs
- Isolamento de dependências
- Mocks específicos por teste
- Limpeza automática entre testes

## 🔧 Configuração

### Jest Setup
- Timeout de 10 segundos
- Setup automático de mocks
- Matchers customizados carregados automaticamente
- Module name mapping para imports relativos

### Coverage
- Coleta de coverage de todos os arquivos src
- Exclusão de arquivos de teste
- Relatórios em texto, LCOV e HTML

## 📝 Exemplos de Uso

### Executando Testes Específicos
```bash
# Testar apenas controllers
npm test -- --testPathPattern=controllers

# Testar apenas um arquivo
npm test -- kitchen.controller.spec.ts

# Testar com verbose output
npm test -- --verbose
```

### Debugging
```bash
# Executar em modo debug
npm run test:debug

# Executar com logs detalhados
npm test -- --verbose --no-coverage
```

## 🤝 Contribuindo

Ao adicionar novos testes:

1. Siga a estrutura de diretórios existente
2. Use as factories e helpers disponíveis
3. Mantenha a cobertura de testes alta
4. Documente casos de teste complexos
5. Execute todos os testes antes de fazer commit

## 📚 Referências

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [NestJS Testing](https://docs.nestjs.com/fundamentals/testing)
- [Mongoose Testing](https://mongoosejs.com/docs/jest.html)
