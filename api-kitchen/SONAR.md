# 📊 SonarQube - API Kitchen

Configuração e guia de uso do SonarQube para análise de qualidade de código da API Kitchen.

## 🎯 Objetivo

O SonarQube é utilizado para:
- Análise estática de código
- Detecção de bugs e vulnerabilidades
- Medição de cobertura de testes
- Análise de duplicação de código
- Métricas de complexidade
- Garantia de qualidade e padrões de código

## 🐳 Docker Setup

### Iniciar SonarQube
```bash
# Usando npm scripts
npm run sonar:start

# Ou usando docker-compose diretamente
docker-compose -f docker-compose-sonar.yml up -d
```

### Parar SonarQube
```bash
# Usando npm scripts
npm run sonar:stop

# Ou usando docker-compose diretamente
docker-compose -f docker-compose-sonar.yml down
```

### Verificar logs
```bash
npm run sonar:logs
```

## 🔧 Configuração

### Acesso Inicial
- **URL**: http://localhost:9001
- **Login padrão**: admin
- **<PERSON><PERSON> padr<PERSON>**: admin

### Configuração do Token
1. Acesse SonarQube (http://localhost:9001)
2. Faça login com admin/admin
3. Vá em **My Account > Security**
4. Gere um novo token
5. Atualize o arquivo `sonar-project.properties` com o token

## 🚀 Executando Análises

### Método 1: Script Simples
```bash
npm run sonar:scan
```

### Método 2: Script Avançado (Interativo)
```bash
./sonar-manager.sh
```

### Método 3: Script Avançado (Comando)
```bash
# Iniciar SonarQube
./sonar-manager.sh start

# Executar análise
./sonar-manager.sh scan

# Verificar status
./sonar-manager.sh status

# Parar SonarQube
./sonar-manager.sh stop
```

### Método 4: Manual
```bash
# 1. Executar testes com coverage
npm run test:cov

# 2. Executar análise
sonar-scanner
```

## 📋 Configurações do Projeto

### sonar-project.properties
```properties
sonar.projectKey=api-kitchen
sonar.projectName=API Kitchen - Vitola Lanches
sonar.projectVersion=1.0

sonar.host.url=http://localhost:9001
sonar.login=seu_token_aqui

sonar.sources=src
sonar.tests=test
sonar.test.inclusions=test/**/*.spec.ts
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**
sonar.typescript.lcov.reportPaths=coverage/lcov.info
```

## 📊 Métricas e Quality Gates

### Métricas Principais
- **Cobertura de Código**: > 80%
- **Duplicação**: < 3%
- **Complexidade Ciclomática**: < 10 por função
- **Linhas por Função**: < 50
- **Bugs**: 0
- **Vulnerabilidades**: 0
- **Code Smells**: Minimizar

### Quality Gates
- **Maintainability Rating**: A
- **Reliability Rating**: A
- **Security Rating**: A
- **Coverage**: > 80%
- **Duplicated Lines**: < 3%

## 🔍 Análises Disponíveis

### 1. Análise de Código
- Detecção de bugs
- Code smells
- Vulnerabilidades de segurança
- Complexidade ciclomática
- Duplicação de código

### 2. Cobertura de Testes
- Cobertura de linhas
- Cobertura de branches
- Cobertura de funções
- Arquivos não testados

### 3. Segurança
- Vulnerabilidades conhecidas
- Hotspots de segurança
- Credenciais hardcoded
- Injeção SQL/XSS

## 🛠️ Scripts Disponíveis

```json
{
  "sonar:start": "Iniciar SonarQube",
  "sonar:stop": "Parar SonarQube", 
  "sonar:scan": "Executar análise",
  "sonar:logs": "Visualizar logs"
}
```

## 📁 Estrutura de Arquivos

```
api-kitchen/
├── docker-compose-sonar.yml    # Configuração Docker
├── sonar-project.properties    # Configuração do projeto
├── run_sonar.sh                # Script simples
├── sonar-manager.sh            # Script avançado
├── .sonarqube.yml              # Configurações extras
└── SONAR.md                    # Esta documentação
```

## 🔧 Configurações Avançadas

### Exclusões
- Arquivos de teste (`**/*.spec.ts`)
- Arquivos E2E (`**/*.e2e-spec.ts`)
- Diretório de testes (`**/test/**`)
- Build artifacts (`**/dist/**`)
- Dependencies (`**/node_modules/**`)
- Coverage reports (`**/coverage/**`)

### Regras TypeScript/NestJS
- Decorators obrigatórios (@Injectable, @Controller, etc.)
- Variáveis não utilizadas
- Preferência por const
- Proibição de var
- Complexidade máxima

## 🚨 Troubleshooting

### Problema: SonarQube não inicia
```bash
# Verificar se a porta está em uso
lsof -i :9001

# Verificar logs
docker-compose -f docker-compose-sonar.yml logs sonarqube
```

### Problema: Análise falha
```bash
# Verificar se o coverage foi gerado
ls -la coverage/

# Verificar configuração
cat sonar-project.properties

# Executar com debug
sonar-scanner -X
```

### Problema: Token inválido
1. Gerar novo token no SonarQube
2. Atualizar `sonar-project.properties`
3. Executar análise novamente

## 📈 Relatórios

### Acessar Relatórios
- **Dashboard**: http://localhost:9001/dashboard?id=api-kitchen
- **Issues**: http://localhost:9001/project/issues?id=api-kitchen
- **Coverage**: http://localhost:9001/component_measures?id=api-kitchen&metric=coverage
- **Duplications**: http://localhost:9001/component_measures?id=api-kitchen&metric=duplicated_lines_density

### Exportar Relatórios
- PDF via interface web
- JSON via API REST
- XML via sonar-scanner

## 🔄 Integração CI/CD

### GitHub Actions
```yaml
- name: SonarQube Scan
  run: |
    npm run test:cov
    sonar-scanner
```

### GitLab CI
```yaml
sonarqube:
  script:
    - npm run test:cov
    - sonar-scanner
```

## 📚 Recursos Adicionais

- [Documentação SonarQube](https://docs.sonarqube.org/)
- [SonarQube TypeScript](https://docs.sonarqube.org/latest/analysis/languages/typescript/)
- [Quality Gates](https://docs.sonarqube.org/latest/user-guide/quality-gates/)
- [Métricas](https://docs.sonarqube.org/latest/user-guide/metric-definitions/)

## 🤝 Contribuindo

1. Execute análise antes de commits
2. Mantenha cobertura > 80%
3. Corrija issues críticos
4. Documente mudanças nas configurações
