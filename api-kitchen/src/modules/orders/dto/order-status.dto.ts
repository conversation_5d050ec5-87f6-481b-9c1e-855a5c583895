import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus } from '../models/order-status';
import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';

export class OrderResponseDto {
  @ApiProperty({
    description: 'Id do pedido',
    example: generateNewObjectId(),
  })
  id: string;

  @ApiProperty({
    description: 'Número do pedido',
    example: 123,
  })
  orderNumber: number;

  @ApiProperty({
    description: 'Número do pedido',
    example: OrderStatus.RECEIVED,
  })
  orderStatus: OrderStatus;

  @ApiProperty({
    description: 'Data de recebimento do pedido',
    example: new Date(),
  })
  receivedDate: Date;

  @ApiProperty({
    description: 'Última data de atualização do pedido',
    example: new Date(),
  })
  lastUpdateDate: Date;

  constructor(id: string, orderNumber: number, orderStatus: OrderStatus, receivedDate: Date, lastUpdateDate: Date) {
    this.id = id;
    this.orderNumber = orderNumber;
    this.orderStatus = orderStatus;
    this.receivedDate = receivedDate;
    this.lastUpdateDate = lastUpdateDate;
  }

  static of(order: any): OrderResponseDto {
    return new OrderResponseDto(order._id, order.orderNumber, order.status, order.createDate, order.updateDate);
  }
}
