import { randomBytes } from 'crypto';

/**
 * Generates a secure, unique order number using cryptographically secure random bytes
 * Format: YYYYMMDD-XXXXXX (where XXXXXX is a 6-digit secure random number)
 * 
 * @returns A secure order number as a string
 */
export function generateSecureOrderNumber(): string {
  const today = new Date();
  const datePrefix = today.getFullYear().toString() + 
                    (today.getMonth() + 1).toString().padStart(2, '0') + 
                    today.getDate().toString().padStart(2, '0');
  
  // Generate 3 cryptographically secure random bytes (24 bits)
  // This gives us 16,777,216 possible combinations per day
  const randomBuffer = randomBytes(3);
  const randomNumber = randomBuffer.readUIntBE(0, 3);
  
  // Convert to 6-digit number (pad with zeros if needed)
  const randomSuffix = (randomNumber % 1000000).toString().padStart(6, '0');
  
  return `${datePrefix}-${randomSuffix}`;
}

/**
 * Generates a secure numeric order number for legacy systems
 * Uses timestamp + high-resolution time + secure random component to minimize collision risk
 *
 * @returns A secure numeric order number (1-10 digits, typically 5-8 digits)
 *
 * Algorithm: timestamp(5) * 100000 + hrtime(2) * 1000 + random(3)
 * Range: 0 to ~10,098,999 (theoretical max)
 * Typical: 10,000 to 9,999,999 (most common range)
 */
export function generateSecureNumericOrderNumber(): number {
  // Use microseconds from timestamp for better uniqueness
  const now = Date.now();
  const microseconds = process.hrtime.bigint();

  // Use last 5 digits of timestamp and 2 digits from high-resolution time
  const timestampComponent = (now % 100000); // 5 digits
  const hrTimeComponent = Number(microseconds % 100n); // 2 digits from nanoseconds

  // Generate 3 cryptographically secure random bytes (24 bits)
  const randomBuffer = randomBytes(3);
  const randomComponent = randomBuffer.readUIntBE(0, 3) % 1000; // 3 digits

  // Combine: 5 digits timestamp + 2 digits hrtime + 3 digits random = 10 digit number
  const orderNumber = timestampComponent * 100000 + hrTimeComponent * 1000 + randomComponent;

  return orderNumber;
}

/**
 * Validates if an order number follows the expected format
 * 
 * @param orderNumber The order number to validate
 * @returns True if valid format, false otherwise
 */
export function isValidOrderNumberFormat(orderNumber: string): boolean {
  // Format: YYYYMMDD-XXXXXX
  const pattern = /^\d{8}-\d{6}$/;
  return pattern.test(orderNumber);
}

/**
 * Extracts the date from a formatted order number
 * 
 * @param orderNumber The order number in format YYYYMMDD-XXXXXX
 * @returns Date object or null if invalid format
 */
export function extractDateFromOrderNumber(orderNumber: string): Date | null {
  if (!isValidOrderNumberFormat(orderNumber)) {
    return null;
  }
  
  const dateStr = orderNumber.split('-')[0];
  const year = parseInt(dateStr.substring(0, 4));
  const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
  const day = parseInt(dateStr.substring(6, 8));
  
  return new Date(year, month, day);
}
