import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiReturn } from '../models/api-return.model';

@Injectable()
export class ApiReturnInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        return {
          success: true,
          return: data,
        } as ApiReturn<any>;
      }),
    );
  }
}
