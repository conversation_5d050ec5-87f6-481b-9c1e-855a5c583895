import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Sistema')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({
    summary: 'Informações da API',
    description: 'Retorna informações básicas sobre a API Kitchen'
  })
  @ApiResponse({
    status: 200,
    description: 'Informações da API retornadas com sucesso',
    schema: {
      example: {
        name: 'API Kitchen - Vitola Lanches',
        version: '1.0.0',
        description: 'API para gerenciamento da cozinha',
        endpoints: {
          orders: '/kitchen/orders',
          docs: '/api/docs'
        }
      }
    }
  })
  getApiInfo(): object {
    return this.appService.getApiInfo();
  }

  @Get('health')
  @ApiOperation({
    summary: 'Health Check',
    description: 'Verifica se a API está funcionando corretamente'
  })
  @ApiResponse({
    status: 200,
    description: 'API funcionando corretamente',
    schema: {
      example: {
        status: 'ok',
        timestamp: '2024-01-15T10:30:00.000Z',
        uptime: 12345
      }
    }
  })
  getHealth(): object {
    return this.appService.getHealth();
  }
}
