#!/bin/bash

# Debug build script to identify module resolution issues

set -e

echo "🔍 Debug Build - API Kitchen"
echo "================================"

# Check Node.js version
echo "📋 Environment Info:"
echo "Node.js version: $(node --version)"
echo "Yarn version: $(yarn --version)"
echo "Working directory: $(pwd)"
echo ""

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found!"
    exit 1
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist/
rm -rf node_modules/.cache/ 2>/dev/null || true

# Install dependencies with verbose output
echo "📦 Installing dependencies..."
yarn install --frozen-lockfile --verbose

# Check if critical dependencies are installed
echo "🔍 Checking critical dependencies..."
CRITICAL_DEPS=("@nestjs/axios" "@nestjs/common" "@nestjs/config" "@nestjs/core" "mongoose" "axios" "rxjs")

for dep in "${CRITICAL_DEPS[@]}"; do
    if [ -d "node_modules/$dep" ]; then
        echo "✅ $dep - installed"
    else
        echo "❌ $dep - MISSING"
    fi
done

# Check TypeScript compilation
echo "🔨 Building TypeScript..."
yarn build

# Verify build output
echo "🔍 Verifying build output..."
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

# Check if main files exist
MAIN_FILES=("dist/main.js" "dist/app.module.js")
for file in "${MAIN_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - exists"
    else
        echo "❌ $file - MISSING"
    fi
done

# Check the problematic file
PROBLEM_FILE="dist/modules/shared/http/http-client.service.js"
if [ -f "$PROBLEM_FILE" ]; then
    echo "✅ $PROBLEM_FILE - exists"
    echo "📄 First 20 lines of the problematic file:"
    head -20 "$PROBLEM_FILE"
else
    echo "❌ $PROBLEM_FILE - MISSING"
fi

# Try to run the application locally (not in Docker)
echo "🚀 Testing local execution..."
if node dist/main.js --help 2>/dev/null; then
    echo "✅ Local execution works"
else
    echo "❌ Local execution failed"
    echo "📋 Trying to identify the specific error..."
    node dist/main.js 2>&1 | head -10 || true
fi

echo ""
echo "🏁 Debug build completed"
