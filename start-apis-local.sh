#!/bin/bash

# Script para iniciar todas as APIs localmente
# Primeiro inicia a infraestrutura no Docker, depois as APIs localmente

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                🚀 DESENVOLVIMENTO LOCAL 🚀                   ║"
echo "║                   Vitola Lanches APIs                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] ℹ️  $1${NC}"
}

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    error "Docker não está rodando. Por favor, inicie o Docker Desktop."
fi

# Verificar se as dependências estão instaladas
check_dependencies() {
    info "Verificando dependências..."
    
    # Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js não está instalado. Por favor, instale Node.js 20+"
    fi
    
    # Yarn
    if ! command -v yarn &> /dev/null; then
        warn "Yarn não encontrado. Instalando yarn..."
        npm install -g yarn
    fi
    
    # Java
    if ! command -v java &> /dev/null; then
        error "Java não está instalado. Por favor, instale Java 21+"
    fi
    
    # Maven
    if [ ! -f "api-costumer/mvnw" ]; then
        error "Maven wrapper não encontrado em api-costumer/"
    fi
    
    log "Todas as dependências estão OK"
}

# Instalar dependências das APIs Node.js
install_node_dependencies() {
    info "Instalando dependências das APIs Node.js..."
    
    for api in api-orders api-payments api-kitchen; do
        if [ -d "$api" ]; then
            info "Instalando dependências do $api..."
            cd "$api"
            yarn install --frozen-lockfile
            cd ..
            log "Dependências do $api instaladas"
        else
            warn "Diretório $api não encontrado"
        fi
    done
}

# Verificar arquivos .env
check_env_files() {
    info "Verificando arquivos .env..."
    
    apis=("api-orders" "api-payments" "api-kitchen")
    
    for api in "${apis[@]}"; do
        if [ ! -f "$api/.env" ]; then
            warn "Arquivo .env não encontrado em $api/"
            warn "As variáveis de ambiente podem não estar configuradas corretamente"
        else
            log "Arquivo .env encontrado em $api/"
        fi
    done
}

# Iniciar infraestrutura
start_infrastructure() {
    info "Iniciando infraestrutura (MongoDB, MySQL, Redis, SonarQube)..."
    ./start-vitola.sh dev
    
    # Aguardar serviços estarem prontos
    info "Aguardando serviços estarem prontos..."
    sleep 15
    
    # Verificar se os serviços estão rodando
    services_ready=true
    
    if ! docker ps | grep -q "vitola-mongodb"; then
        error "MongoDB não está rodando"
        services_ready=false
    fi
    
    if ! docker ps | grep -q "vitola-mysql"; then
        error "MySQL não está rodando"
        services_ready=false
    fi
    
    if ! docker ps | grep -q "vitola-redis"; then
        error "Redis não está rodando"
        services_ready=false
    fi
    
    if [ "$services_ready" = true ]; then
        log "Infraestrutura iniciada com sucesso!"
    else
        error "Alguns serviços da infraestrutura falharam ao iniciar"
    fi
}

# Função para iniciar uma API em background
start_api() {
    local api_name=$1
    local api_dir=$2
    local port=$3
    local start_command=$4
    
    info "Iniciando $api_name na porta $port..."
    
    cd "$api_dir"
    
    # Matar processo existente na porta se houver
    if lsof -ti:$port > /dev/null 2>&1; then
        warn "Processo já rodando na porta $port. Matando processo..."
        kill -9 $(lsof -ti:$port) || true
        sleep 2
    fi
    
    # Iniciar API em background
    nohup $start_command > "../logs/${api_name}.log" 2>&1 &
    local pid=$!
    echo $pid > "../logs/${api_name}.pid"
    
    cd ..
    
    # Aguardar API estar pronta
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1 || \
           curl -s "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
            log "$api_name está rodando na porta $port ✅"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "$api_name falhou ao iniciar na porta $port"
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
}

# Criar diretório de logs
mkdir -p logs

# Executar verificações
check_dependencies
check_env_files
install_node_dependencies

# Iniciar infraestrutura
start_infrastructure

# Iniciar APIs
info "Iniciando APIs localmente..."

# API Orders (porta 3001)
start_api "API Orders" "api-orders" "3001" "yarn start:dev"

# API Payments (porta 3002)  
start_api "API Payments" "api-payments" "3002" "yarn start:dev"

# API Kitchen (porta 3003)
start_api "API Kitchen" "api-kitchen" "3003" "yarn start:dev"

# API Costumer (porta 8080)
start_api "API Costumer" "api-costumer" "8080" "./mvnw spring-boot:run"

echo ""
log "🎉 Todas as APIs estão rodando!"
echo ""
echo -e "${CYAN}🌐 APIs Disponíveis:${NC}"
echo -e "${CYAN}┌─────────────────────────────────────────────────────────────┐${NC}"
echo -e "${CYAN}│ 📦 API Orders    │ http://localhost:3001 │ Order Service   │${NC}"
echo -e "${CYAN}│ 💳 API Payments  │ http://localhost:3002 │ Payment Service │${NC}"
echo -e "${CYAN}│ 🍳 API Kitchen   │ http://localhost:3003 │ Kitchen Service │${NC}"
echo -e "${CYAN}│ 👥 API Costumer  │ http://localhost:8080 │ Customer Service│${NC}"
echo -e "${CYAN}└─────────────────────────────────────────────────────────────┘${NC}"
echo ""
echo -e "${CYAN}🗄️ Infraestrutura:${NC}"
echo -e "${CYAN}│ 🗄️  MongoDB      │ localhost:27017       │ Orders/Payments │${NC}"
echo -e "${CYAN}│ 🐬 MySQL         │ localhost:3306        │ Customers       │${NC}"
echo -e "${CYAN}│ 🔴 Redis         │ localhost:6379        │ Cache           │${NC}"
echo -e "${CYAN}│ 📊 SonarQube     │ http://localhost:9000 │ Code Quality    │${NC}"
echo ""
echo -e "${YELLOW}📋 Comandos Úteis:${NC}"
echo -e "${YELLOW}  • Ver logs: tail -f logs/[api-name].log${NC}"
echo -e "${YELLOW}  • Parar tudo: ./stop-apis-local.sh${NC}"
echo -e "${YELLOW}  • Testar APIs: ./test-apis.sh${NC}"
echo ""
echo -e "${GREEN}✨ Sistema pronto para desenvolvimento!${NC}"
